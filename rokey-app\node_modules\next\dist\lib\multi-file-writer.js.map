{"version": 3, "sources": ["../../src/lib/multi-file-writer.ts"], "sourcesContent": ["import path from '../shared/lib/isomorphic/path'\nimport type { CacheFs } from '../shared/lib/utils'\n\n/**\n * A task to be written.\n */\ntype Task = [\n  /**\n   * The directory to create.\n   */\n  directory: string,\n\n  /**\n   * The promise to create the directory.\n   */\n  mkdir: Promise<unknown>,\n\n  /**\n   * The promises to write the files that are dependent on the directory being\n   * created.\n   */\n  writeFile: Promise<unknown>[],\n]\n/**\n * MultiFileWriter is a utility for writing multiple files in parallel that\n * guarantees that all files will be written after their containing directory\n * is created, and that the directory will only be created once.\n */\nexport class MultiFileWriter {\n  /**\n   * The tasks to be written.\n   */\n  private readonly tasks: Task[] = []\n\n  constructor(\n    /**\n     * The file system methods to use.\n     */\n    private readonly fs: Pick<CacheFs, 'mkdir' | 'writeFile'>\n  ) {}\n\n  /**\n   * Finds or creates a task for a directory.\n   *\n   * @param directory - The directory to find or create a task for.\n   * @returns The task for the directory.\n   */\n  private findOrCreateTask(directory: string): Task {\n    // See if this directory already has a task to create it.\n    for (const task of this.tasks) {\n      if (task[0] === directory) {\n        return task\n      }\n    }\n\n    const promise = this.fs.mkdir(directory)\n\n    // Attach a catch handler so that it doesn't throw an unhandled promise\n    // rejection warning.\n    promise.catch(() => {})\n\n    // Otherwise, create a new task for this directory.\n    const task: Task = [directory, promise, []]\n    this.tasks.push(task)\n\n    return task\n  }\n\n  /**\n   * Appends a file to the writer to be written after its containing directory\n   * is created. The file writer should be awaited after all the files have been\n   * appended. Any async operation that occurs between appending and awaiting\n   * may cause an unhandled promise rejection warning and potentially crash the\n   * process.\n   *\n   * @param filePath - The path to the file to write.\n   * @param data - The data to write to the file.\n   */\n  public append(filePath: string, data: Buffer | string): void {\n    // Find or create a task for the directory that contains the file.\n    const task = this.findOrCreateTask(path.dirname(filePath))\n\n    const promise = task[1].then(() => this.fs.writeFile(filePath, data))\n\n    // Attach a catch handler so that it doesn't throw an unhandled promise\n    // rejection warning.\n    promise.catch(() => {})\n\n    // Add the file write to the task AFTER the directory promise has resolved.\n    task[2].push(promise)\n  }\n\n  /**\n   * Returns a promise that resolves when all the files have been written.\n   */\n  public wait(): Promise<unknown> {\n    return Promise.all(this.tasks.flatMap((task) => task[2]))\n  }\n}\n"], "names": ["MultiFileWriter", "constructor", "fs", "tasks", "findOrCreateTask", "directory", "task", "promise", "mkdir", "catch", "push", "append", "filePath", "data", "path", "dirname", "then", "writeFile", "wait", "Promise", "all", "flatMap"], "mappings": ";;;;+BA4BaA;;;eAAAA;;;6DA5BI;;;;;;AA4BV,MAAMA;IAMXC,YACE;;KAEC,GACD,AAAiBC,EAAwC,CACzD;aADiBA,KAAAA;aANFC,QAAgB,EAAE;IAOhC;IAEH;;;;;GAKC,GACD,AAAQC,iBAAiBC,SAAiB,EAAQ;QAChD,yDAAyD;QACzD,KAAK,MAAMC,QAAQ,IAAI,CAACH,KAAK,CAAE;YAC7B,IAAIG,IAAI,CAAC,EAAE,KAAKD,WAAW;gBACzB,OAAOC;YACT;QACF;QAEA,MAAMC,UAAU,IAAI,CAACL,EAAE,CAACM,KAAK,CAACH;QAE9B,uEAAuE;QACvE,qBAAqB;QACrBE,QAAQE,KAAK,CAAC,KAAO;QAErB,mDAAmD;QACnD,MAAMH,OAAa;YAACD;YAAWE;YAAS,EAAE;SAAC;QAC3C,IAAI,CAACJ,KAAK,CAACO,IAAI,CAACJ;QAEhB,OAAOA;IACT;IAEA;;;;;;;;;GASC,GACD,AAAOK,OAAOC,QAAgB,EAAEC,IAAqB,EAAQ;QAC3D,kEAAkE;QAClE,MAAMP,OAAO,IAAI,CAACF,gBAAgB,CAACU,aAAI,CAACC,OAAO,CAACH;QAEhD,MAAML,UAAUD,IAAI,CAAC,EAAE,CAACU,IAAI,CAAC,IAAM,IAAI,CAACd,EAAE,CAACe,SAAS,CAACL,UAAUC;QAE/D,uEAAuE;QACvE,qBAAqB;QACrBN,QAAQE,KAAK,CAAC,KAAO;QAErB,2EAA2E;QAC3EH,IAAI,CAAC,EAAE,CAACI,IAAI,CAACH;IACf;IAEA;;GAEC,GACD,AAAOW,OAAyB;QAC9B,OAAOC,QAAQC,GAAG,CAAC,IAAI,CAACjB,KAAK,CAACkB,OAAO,CAAC,CAACf,OAASA,IAAI,CAAC,EAAE;IACzD;AACF"}