{"version": 3, "sources": ["../../../../src/server/route-modules/app-page/module.ts"], "sourcesContent": ["import type { AppPageRouteDefinition } from '../../route-definitions/app-page-route-definition'\nimport type RenderResult from '../../render-result'\nimport type { RenderOpts } from '../../app-render/types'\nimport type { NextParsedUrlQuery } from '../../request-meta'\nimport type { LoaderTree } from '../../lib/app-dir-module'\n\nimport {\n  renderToHTMLOrFlight,\n  type AppSharedContext,\n} from '../../app-render/app-render'\nimport {\n  RouteModule,\n  type RouteModuleOptions,\n  type RouteModuleHandleContext,\n} from '../route-module'\nimport * as vendoredContexts from './vendored/contexts/entrypoints'\nimport type { BaseNextRequest, BaseNextResponse } from '../../base-http'\nimport type { ServerComponentsHmrCache } from '../../response-cache'\nimport type { FallbackRouteParams } from '../../request/fallback-params'\n\nlet vendoredReactRSC\nlet vendoredReactSSR\n\n// the vendored Reacts are loaded from their original source in the edge runtime\nif (process.env.NEXT_RUNTIME !== 'edge') {\n  vendoredReactRSC = require('./vendored/rsc/entrypoints')\n  vendoredReactSSR = require('./vendored/ssr/entrypoints')\n}\n\n/**\n * The AppPageModule is the type of the module exported by the bundled app page\n * module.\n */\nexport type AppPageModule = typeof import('../../../build/templates/app-page')\n\ntype AppPageUserlandModule = {\n  /**\n   * The tree created in next-app-loader that holds component segments and modules\n   */\n  loaderTree: LoaderTree\n}\n\nexport interface AppPageRouteHandlerContext extends RouteModuleHandleContext {\n  page: string\n  query: NextParsedUrlQuery\n  fallbackRouteParams: FallbackRouteParams | null\n  renderOpts: RenderOpts\n  serverComponentsHmrCache?: ServerComponentsHmrCache\n  sharedContext: AppSharedContext\n}\n\nexport type AppPageRouteModuleOptions = RouteModuleOptions<\n  AppPageRouteDefinition,\n  AppPageUserlandModule\n>\n\nexport class AppPageRouteModule extends RouteModule<\n  AppPageRouteDefinition,\n  AppPageUserlandModule\n> {\n  public render(\n    req: BaseNextRequest,\n    res: BaseNextResponse,\n    context: AppPageRouteHandlerContext\n  ): Promise<RenderResult> {\n    return renderToHTMLOrFlight(\n      req,\n      res,\n      context.page,\n      context.query,\n      context.fallbackRouteParams,\n      context.renderOpts,\n      context.serverComponentsHmrCache,\n      false,\n      context.sharedContext\n    )\n  }\n\n  public warmup(\n    req: BaseNextRequest,\n    res: BaseNextResponse,\n    context: AppPageRouteHandlerContext\n  ): Promise<RenderResult> {\n    return renderToHTMLOrFlight(\n      req,\n      res,\n      context.page,\n      context.query,\n      context.fallbackRouteParams,\n      context.renderOpts,\n      context.serverComponentsHmrCache,\n      true,\n      context.sharedContext\n    )\n  }\n}\n\nconst vendored = {\n  'react-rsc': vendoredReactRSC,\n  'react-ssr': vendoredReactSSR,\n  contexts: vendoredContexts,\n}\n\nexport { renderToHTMLOrFlight, vendored }\n\nexport default AppPageRouteModule\n"], "names": ["renderToHTMLOrFlight", "RouteModule", "vendoredContexts", "vendoredReactRSC", "vendoredReactSSR", "process", "env", "NEXT_RUNTIME", "require", "AppPageRouteModule", "render", "req", "res", "context", "page", "query", "fallbackRouteParams", "renderOpts", "serverComponentsHmrCache", "sharedContext", "warmup", "vendored", "contexts"], "mappings": "AAMA,SACEA,oBAAoB,QAEf,8BAA6B;AACpC,SACEC,WAAW,QAGN,kBAAiB;AACxB,YAAYC,sBAAsB,kCAAiC;AAKnE,IAAIC;AACJ,IAAIC;AAEJ,gFAAgF;AAChF,IAAIC,QAAQC,GAAG,CAACC,YAAY,KAAK,QAAQ;IACvCJ,mBAAmBK,QAAQ;IAC3BJ,mBAAmBI,QAAQ;AAC7B;AA6BA,OAAO,MAAMC,2BAA2BR;IAI/BS,OACLC,GAAoB,EACpBC,GAAqB,EACrBC,OAAmC,EACZ;QACvB,OAAOb,qBACLW,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,mBAAmB,EAC3BH,QAAQI,UAAU,EAClBJ,QAAQK,wBAAwB,EAChC,OACAL,QAAQM,aAAa;IAEzB;IAEOC,OACLT,GAAoB,EACpBC,GAAqB,EACrBC,OAAmC,EACZ;QACvB,OAAOb,qBACLW,KACAC,KACAC,QAAQC,IAAI,EACZD,QAAQE,KAAK,EACbF,QAAQG,mBAAmB,EAC3BH,QAAQI,UAAU,EAClBJ,QAAQK,wBAAwB,EAChC,MACAL,QAAQM,aAAa;IAEzB;AACF;AAEA,MAAME,WAAW;IACf,aAAalB;IACb,aAAaC;IACbkB,UAAUpB;AACZ;AAEA,SAASF,oBAAoB,EAAEqB,QAAQ,GAAE;AAEzC,eAAeZ,mBAAkB"}