"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "_ssr_src_components_OrchestrationCanvas_tsx";
exports.ids = ["_ssr_src_components_OrchestrationCanvas_tsx"];
exports.modules = {

/***/ "(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js":
/*!************************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js ***!
  \************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction ArrowRightIcon({ title, titleId, ...props }, svgRef) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3\"\n    }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(ArrowRightIcon);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction CpuChipIcon({ title, titleId, ...props }, svgRef) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M8.25 3v1.5M4.5 8.25H3m18 0h-1.5M4.5 12H3m18 0h-1.5m-15 3.75H3m18 0h-1.5M8.25 19.5V21M12 3v1.5m0 15V21m3.75-18v1.5m0 15V21m-9-1.5h10.5a2.25 2.25 0 0 0 2.25-2.25V6.75a2.25 2.25 0 0 0-2.25-2.25H6.75A2.25 2.25 0 0 0 4.5 6.75v10.5a2.25 2.25 0 0 0 2.25 2.25Zm.75-12h9v9h-9v-9Z\"\n    }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(CpuChipIcon);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js":
/*!*******************************************************************!*\
  !*** ./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n\nfunction MinusIcon({ title, titleId, ...props }, svgRef) {\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"svg\", Object.assign({\n        xmlns: \"http://www.w3.org/2000/svg\",\n        fill: \"none\",\n        viewBox: \"0 0 24 24\",\n        strokeWidth: 1.5,\n        stroke: \"currentColor\",\n        \"aria-hidden\": \"true\",\n        \"data-slot\": \"icon\",\n        ref: svgRef,\n        \"aria-labelledby\": titleId\n    }, props), title ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"title\", {\n        id: titleId\n    }, title) : null, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"path\", {\n        strokeLinecap: \"round\",\n        strokeLinejoin: \"round\",\n        d: \"M5 12h14\"\n    }));\n}\nconst ForwardRef = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.forwardRef(MinusIcon);\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (ForwardRef);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\n");

/***/ }),

/***/ "(ssr)/./src/components/ChatMessage.tsx":
/*!****************************************!*\
  !*** ./src/components/ChatMessage.tsx ***!
  \****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ChatMessage: () => (/* binding */ ChatMessage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,CheckCircleIcon,CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* __next_internal_client_entry_do_not_use__ ChatMessage auto */ \n\n\nconst ChatMessage = ({ message })=>{\n    const getRoleColor = (roleId)=>{\n        if (!roleId) return 'from-blue-500 to-blue-600'; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            'from-green-500 to-green-600',\n            'from-purple-500 to-purple-600',\n            'from-orange-500 to-orange-600',\n            'from-pink-500 to-pink-600',\n            'from-indigo-500 to-indigo-600',\n            'from-teal-500 to-teal-600',\n            'from-red-500 to-red-600',\n            'from-yellow-500 to-yellow-600'\n        ];\n        const hash = roleId.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (sender, roleId)=>{\n        if (sender === 'moderator') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                lineNumber: 48,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 50,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getMessageTypeIcon = (type)=>{\n        switch(type){\n            case 'assignment':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-blue-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 56,\n                    columnNumber: 16\n                }, undefined);\n            case 'completion':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                    className: \"w-3 h-3 text-green-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 58,\n                    columnNumber: 16\n                }, undefined);\n            case 'handoff':\n                return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_CheckCircleIcon_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                    className: \"w-3 h-3 text-purple-500\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 60,\n                    columnNumber: 16\n                }, undefined);\n            default:\n                return null;\n        }\n    };\n    const formatTime = (timestamp)=>{\n        return timestamp.toLocaleTimeString([], {\n            hour: '2-digit',\n            minute: '2-digit'\n        });\n    };\n    const isFromModerator = message.sender === 'moderator';\n    const roleColor = getRoleColor(message.roleId);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: `flex ${isFromModerator ? 'justify-start' : 'justify-start'} mb-4`,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${roleColor} flex items-center justify-center text-white shadow-sm`,\n                    children: getRoleIcon(message.sender, message.roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 77,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-sm font-semibold ${isFromModerator ? 'text-blue-700' : 'text-gray-700'}`,\n                                    children: message.senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 85,\n                                    columnNumber: 13\n                                }, undefined),\n                                getMessageTypeIcon(message.type) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center\",\n                                    children: getMessageTypeIcon(message.type)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 92,\n                                    columnNumber: 15\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: formatTime(message.timestamp)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                    lineNumber: 97,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 84,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `inline-block px-4 py-3 rounded-2xl shadow-sm ${isFromModerator ? 'bg-blue-50 border border-blue-100' : 'bg-gray-50 border border-gray-100'} ${message.type === 'completion' ? 'border-green-200 bg-green-50' : message.type === 'assignment' ? 'border-blue-200 bg-blue-50' : message.type === 'handoff' ? 'border-purple-200 bg-purple-50' : ''}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: `text-sm leading-relaxed ${isFromModerator ? 'text-blue-900' : 'text-gray-800'} ${message.type === 'completion' ? 'text-green-900' : message.type === 'assignment' ? 'text-blue-900' : message.type === 'handoff' ? 'text-purple-900' : ''}`,\n                                children: message.content.split('\\n').map((line, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((react__WEBPACK_IMPORTED_MODULE_1___default().Fragment), {\n                                        children: [\n                                            line,\n                                            index < message.content.split('\\n').length - 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"br\", {}, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                                lineNumber: 123,\n                                                columnNumber: 70\n                                            }, undefined)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                        lineNumber: 121,\n                                        columnNumber: 17\n                                    }, undefined))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 112,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 103,\n                            columnNumber: 11\n                        }, undefined),\n                        message.type !== 'message' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: `inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${message.type === 'assignment' ? 'bg-blue-100 text-blue-800' : message.type === 'completion' ? 'bg-green-100 text-green-800' : message.type === 'handoff' ? 'bg-purple-100 text-purple-800' : message.type === 'clarification' ? 'bg-yellow-100 text-yellow-800' : 'bg-gray-100 text-gray-800'}`,\n                                children: [\n                                    message.type === 'assignment' && '📋 Task Assignment',\n                                    message.type === 'completion' && '✅ Task Complete',\n                                    message.type === 'handoff' && '🔄 Handoff',\n                                    message.type === 'clarification' && '❓ Clarification'\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                                lineNumber: 132,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                            lineNumber: 131,\n                            columnNumber: 13\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n                    lineNumber: 82,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n            lineNumber: 75,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\ChatMessage.tsx\",\n        lineNumber: 74,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/ChatMessage.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OrchestrationCanvas.tsx":
/*!************************************************!*\
  !*** ./src/components/OrchestrationCanvas.tsx ***!
  \************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationCanvas: () => (/* binding */ OrchestrationCanvas)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useOrchestrationStream */ \"(ssr)/./src/hooks/useOrchestrationStream.ts\");\n/* harmony import */ var _OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./OrchestrationChatroom */ \"(ssr)/./src/components/OrchestrationChatroom.tsx\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/ChatBubbleLeftRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ChatBubbleLeftRightIcon,MinusIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/MinusIcon.js\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationCanvas auto */ \n\n\n\n\nconst OrchestrationCanvas = ({ executionId, onComplete, onError, onCanvasStateChange, forceMaximize = false })=>{\n    const [isCanvasOpen, setIsCanvasOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isMinimized, setIsMinimized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [orchestrationComplete, setOrchestrationComplete] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [finalResult, setFinalResult] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const { events, isConnected, error } = (0,_hooks_useOrchestrationStream__WEBPACK_IMPORTED_MODULE_2__.useOrchestrationStream)(executionId);\n    // Handle orchestration completion\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            const synthesisCompleteEvent = events.find({\n                \"OrchestrationCanvas.useEffect.synthesisCompleteEvent\": (event)=>event.type === 'synthesis_complete'\n            }[\"OrchestrationCanvas.useEffect.synthesisCompleteEvent\"]);\n            if (synthesisCompleteEvent && !orchestrationComplete) {\n                setOrchestrationComplete(true);\n                const result = synthesisCompleteEvent.data?.result || 'Orchestration completed successfully';\n                setFinalResult(result);\n                // Notify parent component\n                if (onComplete) {\n                    onComplete(result);\n                }\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        events,\n        orchestrationComplete,\n        onComplete\n    ]);\n    // Handle errors\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            if (error && onError) {\n                onError(error);\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        error,\n        onError\n    ]);\n    const handleMinimize = ()=>{\n        setIsMinimized(true);\n        // Keep isCanvasOpen as true so component doesn't disappear completely\n        // We'll hide it via CSS transform instead\n        onCanvasStateChange?.(false, true);\n    };\n    const handleMaximize = ()=>{\n        setIsMinimized(false);\n        // isCanvasOpen should already be true, but ensure it\n        setIsCanvasOpen(true);\n        onCanvasStateChange?.(true, false);\n    };\n    // Notify parent of initial canvas state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            onCanvasStateChange?.(isCanvasOpen, isMinimized);\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        isCanvasOpen,\n        isMinimized,\n        onCanvasStateChange\n    ]);\n    // Handle external maximize trigger\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationCanvas.useEffect\": ()=>{\n            if (forceMaximize && isMinimized) {\n                console.log('🎭 [DEBUG] External maximize trigger received!');\n                handleMaximize();\n            }\n        }\n    }[\"OrchestrationCanvas.useEffect\"], [\n        forceMaximize,\n        isMinimized,\n        handleMaximize\n    ]);\n    // Minimized card state - now returns null, will be rendered inline in chat\n    if (isMinimized) {\n        return null;\n    }\n    // Canvas is closed\n    if (!isCanvasOpen) {\n        return null;\n    }\n    // Debug log when rendering\n    console.log('🎭 [DEBUG] OrchestrationCanvas is rendering!', {\n        isCanvasOpen,\n        isMinimized,\n        executionId,\n        shouldBeVisible: isCanvasOpen && !isMinimized,\n        transformClass: isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'\n    });\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: `fixed top-0 right-0 h-full w-1/2 bg-gradient-to-br from-gray-900 via-black to-gray-900 shadow-2xl z-[9999] transform transition-all duration-500 ease-out ${isCanvasOpen && !isMinimized ? 'translate-x-0' : 'translate-x-full'}`,\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"absolute inset-0 bg-gradient-to-r from-orange-500/10 via-transparent to-orange-500/10 pointer-events-none\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 111,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative flex items-center justify-between p-6 border-b border-orange-500/20 bg-gradient-to-r from-black/80 via-gray-900/90 to-black/80 backdrop-blur-sm\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-0 left-0 right-0 h-[1px] bg-gradient-to-r from-transparent via-orange-500/50 to-transparent\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 116,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-10 h-10 bg-gradient-to-br from-orange-500 to-orange-600 rounded-xl flex items-center justify-center shadow-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"w-5 h-5 text-white\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                lineNumber: 122,\n                                                columnNumber: 17\n                                            }, undefined)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 121,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-orange-500/30 rounded-xl blur-md -z-10 animate-pulse\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 124,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 120,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                            className: \"font-bold text-white text-lg tracking-wide\",\n                                            children: \"AI Team Collaboration\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 128,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center space-x-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-2 h-2 rounded-full ${orchestrationComplete ? 'bg-green-400' : 'bg-orange-400'} animate-pulse`\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                    lineNumber: 130,\n                                                    columnNumber: 17\n                                                }, undefined),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                    className: \"text-sm text-gray-300 font-medium\",\n                                                    children: orchestrationComplete ? 'Mission Complete' : 'Team Active'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                                    lineNumber: 131,\n                                                    columnNumber: 17\n                                                }, undefined)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 129,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 127,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 118,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center space-x-2 px-3 py-1.5 bg-orange-500/10 border border-orange-500/20 rounded-full\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                            className: \"w-4 h-4 text-orange-400\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 141,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-xs text-orange-300 font-medium\",\n                                            children: \"LIVE\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 142,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 140,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: handleMinimize,\n                                    className: \"group relative p-2.5 text-gray-400 hover:text-white hover:bg-orange-500/20 rounded-xl transition-all duration-300 border border-transparent hover:border-orange-500/30\",\n                                    \"aria-label\": \"Minimize canvas\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ChatBubbleLeftRightIcon_MinusIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                            className: \"w-5 h-5 transition-transform group-hover:scale-110\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 151,\n                                            columnNumber: 15\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-orange-500/20 rounded-xl opacity-0 group-hover:opacity-100 blur-sm transition-opacity duration-300 -z-10\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 15\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 138,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 114,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 h-full overflow-hidden relative\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute inset-0 opacity-5\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[radial-gradient(circle_at_50%_50%,rgba(255,107,53,0.1),transparent_50%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 161,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute inset-0 bg-[linear-gradient(90deg,transparent_0%,rgba(255,107,53,0.05)_50%,transparent_100%)]\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                                    lineNumber: 162,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 160,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_OrchestrationChatroom__WEBPACK_IMPORTED_MODULE_3__.OrchestrationChatroom, {\n                            executionId: executionId,\n                            events: events,\n                            isConnected: isConnected,\n                            error: error,\n                            isComplete: orchestrationComplete\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                            lineNumber: 165,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n                    lineNumber: 158,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationCanvas.tsx\",\n            lineNumber: 107,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OrchestrationCanvas.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/OrchestrationChatroom.tsx":
/*!**************************************************!*\
  !*** ./src/components/OrchestrationChatroom.tsx ***!
  \**************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   OrchestrationChatroom: () => (/* binding */ OrchestrationChatroom)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _ChatMessage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./ChatMessage */ \"(ssr)/./src/components/ChatMessage.tsx\");\n/* harmony import */ var _TypingIndicator__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./TypingIndicator */ \"(ssr)/./src/components/TypingIndicator.tsx\");\n/* __next_internal_client_entry_do_not_use__ OrchestrationChatroom auto */ \n\n\n\nconst OrchestrationChatroom = ({ executionId, events, isConnected, error, isComplete })=>{\n    const [chatMessages, setChatMessages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [typingSpecialists, setTypingSpecialists] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(new Set());\n    const messagesEndRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    // Scroll to bottom when new messages arrive\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationChatroom.useEffect\": ()=>{\n            messagesEndRef.current?.scrollIntoView({\n                behavior: 'smooth'\n            });\n        }\n    }[\"OrchestrationChatroom.useEffect\"], [\n        chatMessages\n    ]);\n    // Convert orchestration events to chat messages\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"OrchestrationChatroom.useEffect\": ()=>{\n            const newMessages = [];\n            const currentlyTyping = new Set();\n            events.forEach({\n                \"OrchestrationChatroom.useEffect\": (event, index)=>{\n                    const timestamp = new Date(event.timestamp || Date.now());\n                    const messageId = `${executionId}-${index}`;\n                    switch(event.type){\n                        case 'orchestration_started':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: \"🎬 Welcome to the AI Team Collaboration! I'm assembling the perfect team for this task.\",\n                                timestamp,\n                                type: 'message'\n                            });\n                            break;\n                        case 'task_decomposed':\n                            const steps = event.data?.steps || [];\n                            const teamIntro = steps.map({\n                                \"OrchestrationChatroom.useEffect.teamIntro\": (step)=>`🤖 @${step.roleId} - ${step.modelName || 'AI Specialist'}`\n                            }[\"OrchestrationChatroom.useEffect.teamIntro\"]).join('\\n');\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: `📋 I've analyzed the task and assembled this expert team:\\n\\n${teamIntro}\\n\\nLet's begin the collaboration!`,\n                                timestamp,\n                                type: 'assignment'\n                            });\n                            break;\n                        case 'step_assigned':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                roleId: event.role_id,\n                                content: `🎯 @${event.role_id}, you're up! ${event.data?.commentary || 'Please begin your specialized work on this task.'}`,\n                                timestamp,\n                                type: 'assignment'\n                            });\n                            break;\n                        case 'moderator_assignment':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                roleId: event.role_id,\n                                content: event.data?.message || `🎯 @${event.role_id}, you're up! Please begin your specialized work on this task.`,\n                                timestamp,\n                                type: 'assignment'\n                            });\n                            break;\n                        case 'specialist_acknowledgment':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'specialist',\n                                senderName: event.role_id || 'Specialist',\n                                roleId: event.role_id,\n                                content: event.data?.message || `✅ Understood! I'm ${event.role_id} and I'll handle this task with expertise. Starting work now...`,\n                                timestamp,\n                                type: 'message'\n                            });\n                            break;\n                        case 'step_started':\n                            // Add to typing indicators\n                            if (event.role_id) {\n                                currentlyTyping.add(event.role_id);\n                            }\n                            break;\n                        case 'step_progress':\n                            if (event.role_id) {\n                                currentlyTyping.add(event.role_id);\n                            }\n                            break;\n                        case 'specialist_message':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'specialist',\n                                senderName: event.role_id || 'Specialist',\n                                roleId: event.role_id,\n                                content: `${event.data?.message || '🎉 Perfect! I\\'ve completed my part of the task. Here\\'s what I\\'ve delivered:'}\\n\\n${event.data?.output || 'Task completed successfully!'}`,\n                                timestamp,\n                                type: 'completion'\n                            });\n                            break;\n                        case 'step_completed':\n                            // Remove from typing\n                            if (event.role_id) {\n                                currentlyTyping.delete(event.role_id);\n                            }\n                            break;\n                        case 'handoff_message':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: event.data?.message || `✨ Excellent work, @${event.data?.fromRole}! Quality looks great. Now passing to @${event.data?.toRole}...`,\n                                timestamp,\n                                type: 'handoff'\n                            });\n                            break;\n                        case 'synthesis_started':\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: event.data?.message || `🧩 Fantastic teamwork everyone! Now I'll synthesize all your excellent contributions into the final deliverable...`,\n                                timestamp,\n                                type: 'message'\n                            });\n                            currentlyTyping.add('moderator');\n                            break;\n                        case 'synthesis_complete':\n                            currentlyTyping.delete('moderator');\n                            newMessages.push({\n                                id: messageId,\n                                sender: 'moderator',\n                                senderName: 'Moderator',\n                                content: event.data?.message || `🎊 Mission accomplished! The team has delivered an outstanding result. Great collaboration everyone!`,\n                                timestamp,\n                                type: 'completion'\n                            });\n                            break;\n                    }\n                }\n            }[\"OrchestrationChatroom.useEffect\"]);\n            setChatMessages(newMessages);\n            setTypingSpecialists(currentlyTyping);\n        }\n    }[\"OrchestrationChatroom.useEffect\"], [\n        events,\n        executionId\n    ]);\n    if (error) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex-1 flex items-center justify-center p-8\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"w-12 h-12 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                            className: \"w-6 h-6 text-red-600\",\n                            fill: \"none\",\n                            stroke: \"currentColor\",\n                            viewBox: \"0 0 24 24\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                strokeLinecap: \"round\",\n                                strokeLinejoin: \"round\",\n                                strokeWidth: 2,\n                                d: \"M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 201,\n                                columnNumber: 15\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, undefined)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 199,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"text-lg font-semibold text-gray-900 mb-2\",\n                        children: \"Connection Error\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 204,\n                        columnNumber: 11\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"text-gray-600\",\n                        children: error\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 205,\n                        columnNumber: 11\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, undefined)\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, undefined);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex flex-col h-full\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: `px-4 py-2 text-xs font-medium ${isConnected ? 'bg-green-50 text-green-700 border-b border-green-100' : 'bg-yellow-50 text-yellow-700 border-b border-yellow-100'}`,\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center space-x-2\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-yellow-500'}`\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 220,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            children: isConnected ? 'Connected to AI Team' : 'Connecting...'\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 221,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                    lineNumber: 219,\n                    columnNumber: 9\n                }, undefined)\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 214,\n                columnNumber: 7\n            }, undefined),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex-1 overflow-y-auto p-4 space-y-4\",\n                children: [\n                    chatMessages.length === 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center py-8\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                    className: \"w-6 h-6 text-blue-600 animate-pulse\",\n                                    fill: \"none\",\n                                    stroke: \"currentColor\",\n                                    viewBox: \"0 0 24 24\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                        strokeLinecap: \"round\",\n                                        strokeLinejoin: \"round\",\n                                        strokeWidth: 2,\n                                        d: \"M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                        lineNumber: 231,\n                                        columnNumber: 17\n                                    }, undefined)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                    lineNumber: 230,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, undefined),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-500\",\n                                children: \"Waiting for AI team to start collaboration...\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                                lineNumber: 234,\n                                columnNumber: 13\n                            }, undefined)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, undefined),\n                    chatMessages.map((message)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_ChatMessage__WEBPACK_IMPORTED_MODULE_2__.ChatMessage, {\n                            message: message\n                        }, message.id, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 239,\n                            columnNumber: 11\n                        }, undefined)),\n                    Array.from(typingSpecialists).map((specialist)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_TypingIndicator__WEBPACK_IMPORTED_MODULE_3__.TypingIndicator, {\n                            senderName: specialist,\n                            roleId: specialist !== 'moderator' ? specialist : undefined\n                        }, specialist, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                            lineNumber: 247,\n                            columnNumber: 11\n                        }, undefined)),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        ref: messagesEndRef\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                        lineNumber: 254,\n                        columnNumber: 9\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n                lineNumber: 226,\n                columnNumber: 7\n            }, undefined)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\OrchestrationChatroom.tsx\",\n        lineNumber: 212,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/OrchestrationChatroom.tsx\n");

/***/ }),

/***/ "(ssr)/./src/components/TypingIndicator.tsx":
/*!********************************************!*\
  !*** ./src/components/TypingIndicator.tsx ***!
  \********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   TypingIndicator: () => (/* binding */ TypingIndicator)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=CpuChipIcon,SparklesIcon!=!@heroicons/react/24/outline */ \"(ssr)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* __next_internal_client_entry_do_not_use__ TypingIndicator auto */ \n\n\nconst TypingIndicator = ({ senderName, roleId })=>{\n    const getRoleColor = (roleId)=>{\n        if (!roleId || roleId === 'moderator') return 'from-blue-500 to-blue-600'; // Moderator\n        // Generate consistent colors based on role name\n        const colors = [\n            'from-green-500 to-green-600',\n            'from-purple-500 to-purple-600',\n            'from-orange-500 to-orange-600',\n            'from-pink-500 to-pink-600',\n            'from-indigo-500 to-indigo-600',\n            'from-teal-500 to-teal-600',\n            'from-red-500 to-red-600',\n            'from-yellow-500 to-yellow-600'\n        ];\n        const hash = roleId.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return colors[hash % colors.length];\n    };\n    const getRoleIcon = (roleId)=>{\n        if (!roleId || roleId === 'moderator') {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_2__[\"default\"], {\n                className: \"w-4 h-4\"\n            }, void 0, false, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                lineNumber: 36,\n                columnNumber: 14\n            }, undefined);\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_CpuChipIcon_SparklesIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"], {\n            className: \"w-4 h-4\"\n        }, void 0, false, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 38,\n            columnNumber: 12\n        }, undefined);\n    };\n    const getTypingMessage = (senderName)=>{\n        const messages = [\n            'is thinking...',\n            'is working on this...',\n            'is analyzing...',\n            'is processing...',\n            'is crafting a response...'\n        ];\n        // Use sender name to consistently pick a message\n        const hash = senderName.split('').reduce((acc, char)=>acc + char.charCodeAt(0), 0);\n        return messages[hash % messages.length];\n    };\n    const roleColor = getRoleColor(roleId);\n    const isFromModerator = !roleId || roleId === 'moderator';\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"flex justify-start mb-4 opacity-75\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-start space-x-3 max-w-[85%]\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: `flex-shrink-0 w-8 h-8 rounded-full bg-gradient-to-r ${roleColor} flex items-center justify-center text-white shadow-sm animate-pulse`,\n                    children: getRoleIcon(roleId)\n                }, void 0, false, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 62,\n                    columnNumber: 9\n                }, undefined),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex-1 min-w-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center space-x-2 mb-1\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: `text-sm font-semibold ${isFromModerator ? 'text-blue-700' : 'text-gray-700'}`,\n                                    children: senderName\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                    lineNumber: 70,\n                                    columnNumber: 13\n                                }, undefined),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-xs text-gray-500\",\n                                    children: getTypingMessage(senderName)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, undefined)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                            lineNumber: 69,\n                            columnNumber: 11\n                        }, undefined),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: `inline-block px-4 py-3 rounded-2xl shadow-sm ${isFromModerator ? 'bg-blue-50 border border-blue-100' : 'bg-gray-50 border border-gray-100'}`,\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-1\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex space-x-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '0ms'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                            lineNumber: 88,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '150ms'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 17\n                                        }, undefined),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"w-2 h-2 bg-gray-400 rounded-full animate-bounce\",\n                                            style: {\n                                                animationDelay: '300ms'\n                                            }\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                            lineNumber: 90,\n                                            columnNumber: 17\n                                        }, undefined)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                    lineNumber: 87,\n                                    columnNumber: 15\n                                }, undefined)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                                lineNumber: 86,\n                                columnNumber: 13\n                            }, undefined)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, undefined)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n                    lineNumber: 67,\n                    columnNumber: 9\n                }, undefined)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n            lineNumber: 60,\n            columnNumber: 7\n        }, undefined)\n    }, void 0, false, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\TypingIndicator.tsx\",\n        lineNumber: 59,\n        columnNumber: 5\n    }, undefined);\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/components/TypingIndicator.tsx\n");

/***/ }),

/***/ "(ssr)/./src/hooks/useOrchestrationStream.ts":
/*!*********************************************!*\
  !*** ./src/hooks/useOrchestrationStream.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useLatestOrchestrationEvent: () => (/* binding */ useLatestOrchestrationEvent),\n/* harmony export */   useOrchestrationEventsByType: () => (/* binding */ useOrchestrationEventsByType),\n/* harmony export */   useOrchestrationProgress: () => (/* binding */ useOrchestrationProgress),\n/* harmony export */   useOrchestrationStream: () => (/* binding */ useOrchestrationStream)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useOrchestrationStream,useOrchestrationEventsByType,useLatestOrchestrationEvent,useOrchestrationProgress auto */ \nfunction useOrchestrationStream(executionId, directStreamUrl) {\n    const [events, setEvents] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const [isConnected, setIsConnected] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [error, setError] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [lastEvent, setLastEvent] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(null);\n    const [streamUrl, setStreamUrl] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('');\n    const eventSourceRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const pollingTimeoutRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(null);\n    const reconnectAttempts = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const maxReconnectAttempts = 5;\n    const baseReconnectDelay = 1000; // 1 second\n    // Track the current execution ID and stream URL to detect changes\n    const currentExecutionIdRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const currentStreamUrlRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)('');\n    const disconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationStream.useCallback[disconnect]\": ()=>{\n            if (eventSourceRef.current) {\n                eventSourceRef.current.close();\n                eventSourceRef.current = null;\n            }\n            if (reconnectTimeoutRef.current) {\n                clearTimeout(reconnectTimeoutRef.current);\n                reconnectTimeoutRef.current = null;\n            }\n            setIsConnected(false);\n        }\n    }[\"useOrchestrationStream.useCallback[disconnect]\"], []);\n    const connect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationStream.useCallback[connect]\": ()=>{\n            if (!executionId && !directStreamUrl) {\n                setError('No execution ID or direct stream URL provided');\n                return;\n            }\n            // Determine the URL to connect to\n            const url = directStreamUrl || (executionId ? `/api/orchestration/stream/${executionId}` : '');\n            if (!url) {\n                setError('No valid stream URL could be determined');\n                return;\n            }\n            // Skip if we're already connected to this URL\n            if (currentStreamUrlRef.current === url && isConnected) {\n                console.log(`[Orchestration Stream] Already connected to: ${url}`);\n                return;\n            }\n            console.log(`[Orchestration Stream] Connecting to: ${url}`);\n            // Clean up existing connection\n            disconnect();\n            // Update refs\n            currentExecutionIdRef.current = executionId || '';\n            currentStreamUrlRef.current = url;\n            try {\n                const eventSource = new EventSource(url);\n                eventSourceRef.current = eventSource;\n                eventSource.onopen = ({\n                    \"useOrchestrationStream.useCallback[connect]\": ()=>{\n                        console.log(`[Orchestration Stream] Connected to execution ${executionId}`);\n                        setIsConnected(true);\n                        setError(null);\n                        reconnectAttempts.current = 0;\n                    }\n                })[\"useOrchestrationStream.useCallback[connect]\"];\n                eventSource.onmessage = ({\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        try {\n                            const orchestrationEvent = JSON.parse(event.data);\n                            console.log(`[Orchestration Stream] Received event:`, orchestrationEvent);\n                            setEvents({\n                                \"useOrchestrationStream.useCallback[connect]\": (prev)=>[\n                                        ...prev,\n                                        orchestrationEvent\n                                    ]\n                            }[\"useOrchestrationStream.useCallback[connect]\"]);\n                            setLastEvent(orchestrationEvent);\n                            // Reset error state on successful message\n                            setError(null);\n                        } catch (parseError) {\n                            console.error('[Orchestration Stream] Error parsing event:', parseError);\n                            setError('Error parsing stream data');\n                        }\n                    }\n                })[\"useOrchestrationStream.useCallback[connect]\"];\n                // Handle specific event types\n                eventSource.addEventListener('orchestration_started', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Orchestration started:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('step_started', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Step started:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('step_progress', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Step progress:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('step_completed', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Step completed:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('synthesis_started', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Synthesis started:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.addEventListener('orchestration_completed', {\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        const data = JSON.parse(event.data);\n                        console.log('[Orchestration Stream] Orchestration completed:', data);\n                    }\n                }[\"useOrchestrationStream.useCallback[connect]\"]);\n                eventSource.onerror = ({\n                    \"useOrchestrationStream.useCallback[connect]\": (event)=>{\n                        console.error('[Orchestration Stream] Connection error:', event);\n                        setIsConnected(false);\n                        // Attempt to reconnect with exponential backoff\n                        if (reconnectAttempts.current < maxReconnectAttempts) {\n                            const delay = baseReconnectDelay * Math.pow(2, reconnectAttempts.current);\n                            reconnectAttempts.current++;\n                            setError(`Connection lost. Reconnecting in ${delay / 1000}s... (attempt ${reconnectAttempts.current}/${maxReconnectAttempts})`);\n                            reconnectTimeoutRef.current = setTimeout({\n                                \"useOrchestrationStream.useCallback[connect]\": ()=>{\n                                    console.log(`[Orchestration Stream] Reconnecting... (attempt ${reconnectAttempts.current})`);\n                                    connect();\n                                }\n                            }[\"useOrchestrationStream.useCallback[connect]\"], delay);\n                        } else {\n                            setError('Connection failed after multiple attempts. Please refresh the page.');\n                        }\n                    }\n                })[\"useOrchestrationStream.useCallback[connect]\"];\n            } catch (connectionError) {\n                console.error('[Orchestration Stream] Failed to create connection:', connectionError);\n                setError('Failed to establish connection');\n                setIsConnected(false);\n            }\n        }\n    }[\"useOrchestrationStream.useCallback[connect]\"], [\n        executionId,\n        disconnect\n    ]);\n    const reconnect = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useOrchestrationStream.useCallback[reconnect]\": ()=>{\n            reconnectAttempts.current = 0;\n            connect();\n        }\n    }[\"useOrchestrationStream.useCallback[reconnect]\"], [\n        connect\n    ]);\n    // Connect on mount and when executionId or directStreamUrl changes\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationStream.useEffect\": ()=>{\n            if (executionId || directStreamUrl) {\n                connect();\n            }\n            // Cleanup on unmount\n            return ({\n                \"useOrchestrationStream.useEffect\": ()=>{\n                    disconnect();\n                }\n            })[\"useOrchestrationStream.useEffect\"];\n        }\n    }[\"useOrchestrationStream.useEffect\"], [\n        executionId,\n        directStreamUrl,\n        connect,\n        disconnect\n    ]);\n    // Handle page visibility changes to reconnect when page becomes visible\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationStream.useEffect\": ()=>{\n            const handleVisibilityChange = {\n                \"useOrchestrationStream.useEffect.handleVisibilityChange\": ()=>{\n                    if (document.visibilityState === 'visible' && !isConnected && (executionId || directStreamUrl)) {\n                        console.log('[Orchestration Stream] Page became visible, attempting to reconnect...');\n                        reconnect();\n                    }\n                }\n            }[\"useOrchestrationStream.useEffect.handleVisibilityChange\"];\n            document.addEventListener('visibilitychange', handleVisibilityChange);\n            return ({\n                \"useOrchestrationStream.useEffect\": ()=>{\n                    document.removeEventListener('visibilitychange', handleVisibilityChange);\n                }\n            })[\"useOrchestrationStream.useEffect\"];\n        }\n    }[\"useOrchestrationStream.useEffect\"], [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    // Handle online/offline events\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useOrchestrationStream.useEffect\": ()=>{\n            const handleOnline = {\n                \"useOrchestrationStream.useEffect.handleOnline\": ()=>{\n                    if (!isConnected && (executionId || directStreamUrl)) {\n                        console.log('[Orchestration Stream] Network came back online, attempting to reconnect...');\n                        reconnect();\n                    }\n                }\n            }[\"useOrchestrationStream.useEffect.handleOnline\"];\n            const handleOffline = {\n                \"useOrchestrationStream.useEffect.handleOffline\": ()=>{\n                    setError('Network connection lost');\n                }\n            }[\"useOrchestrationStream.useEffect.handleOffline\"];\n            window.addEventListener('online', handleOnline);\n            window.addEventListener('offline', handleOffline);\n            return ({\n                \"useOrchestrationStream.useEffect\": ()=>{\n                    window.removeEventListener('online', handleOnline);\n                    window.removeEventListener('offline', handleOffline);\n                }\n            })[\"useOrchestrationStream.useEffect\"];\n        }\n    }[\"useOrchestrationStream.useEffect\"], [\n        isConnected,\n        executionId,\n        directStreamUrl,\n        reconnect\n    ]);\n    return {\n        events,\n        isConnected,\n        error,\n        lastEvent,\n        reconnect,\n        disconnect\n    };\n}\n// Helper hook for filtering events by type\nfunction useOrchestrationEventsByType(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    return events.filter((event)=>event.type === eventType);\n}\n// Helper hook for getting the latest event of a specific type\nfunction useLatestOrchestrationEvent(executionId, eventType) {\n    const { events } = useOrchestrationStream(executionId);\n    const filteredEvents = events.filter((event)=>event.type === eventType);\n    return filteredEvents.length > 0 ? filteredEvents[filteredEvents.length - 1] : null;\n}\n// Helper hook for tracking orchestration progress\nfunction useOrchestrationProgress(executionId) {\n    const { events } = useOrchestrationStream(executionId);\n    const stepStartedEvents = events.filter((e)=>e.type === 'step_started');\n    const stepCompletedEvents = events.filter((e)=>e.type === 'step_completed');\n    const orchestrationCompleted = events.some((e)=>e.type === 'orchestration_completed');\n    const totalSteps = stepStartedEvents.length;\n    const completedSteps = stepCompletedEvents.length;\n    const currentStep = totalSteps > 0 ? Math.min(completedSteps + 1, totalSteps) : 0;\n    const progress = totalSteps > 0 ? completedSteps / totalSteps * 100 : 0;\n    return {\n        totalSteps,\n        completedSteps,\n        currentStep,\n        progress,\n        isComplete: orchestrationCompleted\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/hooks/useOrchestrationStream.ts\n");

/***/ })

};
;