'use strict';

module.exports = {
	abs: {
		url: 'https://262.ecma-international.org/12.0/#eqn-abs'
	},
	'Abstract Equality Comparison': {
		url: 'https://262.ecma-international.org/12.0/#sec-abstract-equality-comparison'
	},
	'Abstract Relational Comparison': {
		url: 'https://262.ecma-international.org/12.0/#sec-abstract-relational-comparison'
	},
	AddEntriesFromIterable: {
		url: 'https://262.ecma-international.org/12.0/#sec-add-entries-from-iterable'
	},
	AddRestrictedFunctionProperties: {
		url: 'https://262.ecma-international.org/12.0/#sec-addrestrictedfunctionproperties'
	},
	AddToKeptObjects: {
		url: 'https://262.ecma-international.org/12.0/#sec-addtokeptobjects'
	},
	AddWaiter: {
		url: 'https://262.ecma-international.org/12.0/#sec-addwaiter'
	},
	AdvanceStringIndex: {
		url: 'https://262.ecma-international.org/12.0/#sec-advancestringindex'
	},
	'agent-order': {
		url: 'https://262.ecma-international.org/12.0/#sec-agent-order'
	},
	AgentCanSuspend: {
		url: 'https://262.ecma-international.org/12.0/#sec-agentcansuspend'
	},
	AgentSignifier: {
		url: 'https://262.ecma-international.org/12.0/#sec-agentsignifier'
	},
	AllocateArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-allocatearraybuffer'
	},
	AllocateSharedArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-allocatesharedarraybuffer'
	},
	AllocateTypedArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-allocatetypedarray'
	},
	AllocateTypedArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-allocatetypedarraybuffer'
	},
	ApplyStringOrNumericBinaryOperator: {
		url: 'https://262.ecma-international.org/12.0/#sec-applystringornumericbinaryoperator'
	},
	ArrayCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-arraycreate'
	},
	ArraySetLength: {
		url: 'https://262.ecma-international.org/12.0/#sec-arraysetlength'
	},
	ArraySpeciesCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-arrayspeciescreate'
	},
	AsyncFromSyncIteratorContinuation: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncfromsynciteratorcontinuation'
	},
	AsyncFunctionStart: {
		url: 'https://262.ecma-international.org/12.0/#sec-async-functions-abstract-operations-async-function-start'
	},
	AsyncGeneratorEnqueue: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratorenqueue'
	},
	AsyncGeneratorReject: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratorreject'
	},
	AsyncGeneratorResolve: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratorresolve'
	},
	AsyncGeneratorResumeNext: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratorresumenext'
	},
	AsyncGeneratorStart: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratorstart'
	},
	AsyncGeneratorValidate: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratorvalidate'
	},
	AsyncGeneratorYield: {
		url: 'https://262.ecma-international.org/12.0/#sec-asyncgeneratoryield'
	},
	AsyncIteratorClose: {
		url: 'https://262.ecma-international.org/12.0/#sec-asynciteratorclose'
	},
	AtomicReadModifyWrite: {
		url: 'https://262.ecma-international.org/12.0/#sec-atomicreadmodifywrite'
	},
	Await: {
		url: 'https://262.ecma-international.org/12.0/#await'
	},
	BackreferenceMatcher: {
		url: 'https://262.ecma-international.org/12.0/#sec-backreference-matcher'
	},
	'BigInt::add': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-add'
	},
	'BigInt::bitwiseAND': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-bitwiseAND'
	},
	'BigInt::bitwiseNOT': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-bitwiseNOT'
	},
	'BigInt::bitwiseOR': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-bitwiseOR'
	},
	'BigInt::bitwiseXOR': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-bitwiseXOR'
	},
	'BigInt::divide': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-divide'
	},
	'BigInt::equal': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-equal'
	},
	'BigInt::exponentiate': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-exponentiate'
	},
	'BigInt::leftShift': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-leftShift'
	},
	'BigInt::lessThan': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-lessThan'
	},
	'BigInt::multiply': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-multiply'
	},
	'BigInt::remainder': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-remainder'
	},
	'BigInt::sameValue': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-sameValue'
	},
	'BigInt::sameValueZero': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-sameValueZero'
	},
	'BigInt::signedRightShift': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-signedRightShift'
	},
	'BigInt::subtract': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-subtract'
	},
	'BigInt::toString': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-tostring'
	},
	'BigInt::unaryMinus': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-unaryMinus'
	},
	'BigInt::unsignedRightShift': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-bigint-unsignedRightShift'
	},
	BigIntBitwiseOp: {
		url: 'https://262.ecma-international.org/12.0/#sec-bigintbitwiseop'
	},
	BinaryAnd: {
		url: 'https://262.ecma-international.org/12.0/#sec-binaryand'
	},
	BinaryOr: {
		url: 'https://262.ecma-international.org/12.0/#sec-binaryor'
	},
	BinaryXor: {
		url: 'https://262.ecma-international.org/12.0/#sec-binaryxor'
	},
	BlockDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/12.0/#sec-blockdeclarationinstantiation'
	},
	BoundFunctionCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-boundfunctioncreate'
	},
	ByteListBitwiseOp: {
		url: 'https://262.ecma-international.org/12.0/#sec-bytelistbitwiseop'
	},
	ByteListEqual: {
		url: 'https://262.ecma-international.org/12.0/#sec-bytelistequal'
	},
	Call: {
		url: 'https://262.ecma-international.org/12.0/#sec-call'
	},
	Canonicalize: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-canonicalize-ch'
	},
	CanonicalNumericIndexString: {
		url: 'https://262.ecma-international.org/12.0/#sec-canonicalnumericindexstring'
	},
	CaseClauseIsSelected: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-caseclauseisselected'
	},
	CharacterRange: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-characterrange-abstract-operation'
	},
	CharacterRangeOrUnion: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-characterrangeorunion-abstract-operation'
	},
	CharacterSetMatcher: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-charactersetmatcher-abstract-operation'
	},
	clamp: {
		url: 'https://262.ecma-international.org/12.0/#clamping'
	},
	CleanupFinalizationRegistry: {
		url: 'https://262.ecma-international.org/12.0/#sec-cleanup-finalization-registry'
	},
	ClearKeptObjects: {
		url: 'https://262.ecma-international.org/12.0/#sec-clear-kept-objects'
	},
	CloneArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-clonearraybuffer'
	},
	CodePointAt: {
		url: 'https://262.ecma-international.org/12.0/#sec-codepointat'
	},
	CodePointsToString: {
		url: 'https://262.ecma-international.org/12.0/#sec-codepointstostring'
	},
	CompletePropertyDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-completepropertydescriptor'
	},
	Completion: {
		url: 'https://262.ecma-international.org/12.0/#sec-completion-record-specification-type'
	},
	CompletionRecord: {
		url: 'https://262.ecma-international.org/12.0/#sec-completion-record-specification-type'
	},
	ComposeWriteEventBytes: {
		url: 'https://262.ecma-international.org/12.0/#sec-composewriteeventbytes'
	},
	Construct: {
		url: 'https://262.ecma-international.org/12.0/#sec-construct'
	},
	CopyDataBlockBytes: {
		url: 'https://262.ecma-international.org/12.0/#sec-copydatablockbytes'
	},
	CopyDataProperties: {
		url: 'https://262.ecma-international.org/12.0/#sec-copydataproperties'
	},
	CreateArrayFromList: {
		url: 'https://262.ecma-international.org/12.0/#sec-createarrayfromlist'
	},
	CreateArrayIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-createarrayiterator'
	},
	CreateAsyncFromSyncIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-createasyncfromsynciterator'
	},
	CreateAsyncIteratorFromClosure: {
		url: 'https://262.ecma-international.org/12.0/#sec-createasynciteratorfromclosure'
	},
	CreateBuiltinFunction: {
		url: 'https://262.ecma-international.org/12.0/#sec-createbuiltinfunction'
	},
	CreateByteDataBlock: {
		url: 'https://262.ecma-international.org/12.0/#sec-createbytedatablock'
	},
	CreateDataProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-createdataproperty'
	},
	CreateDataPropertyOrThrow: {
		url: 'https://262.ecma-international.org/12.0/#sec-createdatapropertyorthrow'
	},
	CreateDynamicFunction: {
		url: 'https://262.ecma-international.org/12.0/#sec-createdynamicfunction'
	},
	CreateForInIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-createforiniterator'
	},
	CreateHTML: {
		url: 'https://262.ecma-international.org/12.0/#sec-createhtml'
	},
	CreateIntrinsics: {
		url: 'https://262.ecma-international.org/12.0/#sec-createintrinsics'
	},
	CreateIteratorFromClosure: {
		url: 'https://262.ecma-international.org/12.0/#sec-createiteratorfromclosure'
	},
	CreateIterResultObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-createiterresultobject'
	},
	CreateListFromArrayLike: {
		url: 'https://262.ecma-international.org/12.0/#sec-createlistfromarraylike'
	},
	CreateListIteratorRecord: {
		url: 'https://262.ecma-international.org/12.0/#sec-createlistiteratorRecord'
	},
	CreateMapIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-createmapiterator'
	},
	CreateMappedArgumentsObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-createmappedargumentsobject'
	},
	CreateMethodProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-createmethodproperty'
	},
	CreatePerIterationEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-createperiterationenvironment'
	},
	CreateRealm: {
		url: 'https://262.ecma-international.org/12.0/#sec-createrealm'
	},
	CreateRegExpStringIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-createregexpstringiterator'
	},
	CreateResolvingFunctions: {
		url: 'https://262.ecma-international.org/12.0/#sec-createresolvingfunctions'
	},
	CreateSetIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-createsetiterator'
	},
	CreateSharedByteDataBlock: {
		url: 'https://262.ecma-international.org/12.0/#sec-createsharedbytedatablock'
	},
	CreateUnmappedArgumentsObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-createunmappedargumentsobject'
	},
	DateFromTime: {
		url: 'https://262.ecma-international.org/12.0/#sec-date-number'
	},
	DateString: {
		url: 'https://262.ecma-international.org/12.0/#sec-datestring'
	},
	Day: {
		url: 'https://262.ecma-international.org/12.0/#eqn-Day'
	},
	DayFromYear: {
		url: 'https://262.ecma-international.org/12.0/#eqn-DaysFromYear'
	},
	DaysInYear: {
		url: 'https://262.ecma-international.org/12.0/#eqn-DaysInYear'
	},
	DayWithinYear: {
		url: 'https://262.ecma-international.org/12.0/#eqn-DayWithinYear'
	},
	Decode: {
		url: 'https://262.ecma-international.org/12.0/#sec-decode'
	},
	DefinePropertyOrThrow: {
		url: 'https://262.ecma-international.org/12.0/#sec-definepropertyorthrow'
	},
	DeletePropertyOrThrow: {
		url: 'https://262.ecma-international.org/12.0/#sec-deletepropertyorthrow'
	},
	DetachArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-detacharraybuffer'
	},
	Encode: {
		url: 'https://262.ecma-international.org/12.0/#sec-encode'
	},
	EnterCriticalSection: {
		url: 'https://262.ecma-international.org/12.0/#sec-entercriticalsection'
	},
	EnumerableOwnPropertyNames: {
		url: 'https://262.ecma-international.org/12.0/#sec-enumerableownpropertynames'
	},
	EnumerateObjectProperties: {
		url: 'https://262.ecma-international.org/12.0/#sec-enumerate-object-properties'
	},
	EscapeRegExpPattern: {
		url: 'https://262.ecma-international.org/12.0/#sec-escaperegexppattern'
	},
	EvalDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/12.0/#sec-evaldeclarationinstantiation'
	},
	EvaluateCall: {
		url: 'https://262.ecma-international.org/12.0/#sec-evaluatecall'
	},
	EvaluateNew: {
		url: 'https://262.ecma-international.org/12.0/#sec-evaluatenew'
	},
	EvaluatePropertyAccessWithExpressionKey: {
		url: 'https://262.ecma-international.org/12.0/#sec-evaluate-property-access-with-expression-key'
	},
	EvaluatePropertyAccessWithIdentifierKey: {
		url: 'https://262.ecma-international.org/12.0/#sec-evaluate-property-access-with-identifier-key'
	},
	EvaluateStringOrNumericBinaryExpression: {
		url: 'https://262.ecma-international.org/12.0/#sec-evaluatestringornumericbinaryexpression'
	},
	EventSet: {
		url: 'https://262.ecma-international.org/12.0/#sec-event-set'
	},
	ExecuteModule: {
		url: 'https://262.ecma-international.org/12.0/#sec-source-text-module-record-execute-module'
	},
	FinishDynamicImport: {
		url: 'https://262.ecma-international.org/12.0/#sec-finishdynamicimport'
	},
	FlattenIntoArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-flattenintoarray'
	},
	floor: {
		url: 'https://262.ecma-international.org/12.0/#eqn-floor'
	},
	ForBodyEvaluation: {
		url: 'https://262.ecma-international.org/12.0/#sec-forbodyevaluation'
	},
	'ForIn/OfBodyEvaluation': {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-forin-div-ofbodyevaluation-lhs-stmt-iterator-lhskind-labelset'
	},
	'ForIn/OfHeadEvaluation': {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-forinofheadevaluation'
	},
	FromPropertyDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-frompropertydescriptor'
	},
	FulfillPromise: {
		url: 'https://262.ecma-international.org/12.0/#sec-fulfillpromise'
	},
	FunctionDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/12.0/#sec-functiondeclarationinstantiation'
	},
	GeneratorResume: {
		url: 'https://262.ecma-international.org/12.0/#sec-generatorresume'
	},
	GeneratorResumeAbrupt: {
		url: 'https://262.ecma-international.org/12.0/#sec-generatorresumeabrupt'
	},
	GeneratorStart: {
		url: 'https://262.ecma-international.org/12.0/#sec-generatorstart'
	},
	GeneratorValidate: {
		url: 'https://262.ecma-international.org/12.0/#sec-generatorvalidate'
	},
	GeneratorYield: {
		url: 'https://262.ecma-international.org/12.0/#sec-generatoryield'
	},
	Get: {
		url: 'https://262.ecma-international.org/12.0/#sec-get-o-p'
	},
	GetActiveScriptOrModule: {
		url: 'https://262.ecma-international.org/12.0/#sec-getactivescriptormodule'
	},
	GetFunctionRealm: {
		url: 'https://262.ecma-international.org/12.0/#sec-getfunctionrealm'
	},
	GetGeneratorKind: {
		url: 'https://262.ecma-international.org/12.0/#sec-getgeneratorkind'
	},
	GetGlobalObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-getglobalobject'
	},
	GetIdentifierReference: {
		url: 'https://262.ecma-international.org/12.0/#sec-getidentifierreference'
	},
	GetIterator: {
		url: 'https://262.ecma-international.org/12.0/#sec-getiterator'
	},
	GetMethod: {
		url: 'https://262.ecma-international.org/12.0/#sec-getmethod'
	},
	GetModifySetValueInBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-getmodifysetvalueinbuffer'
	},
	GetModuleNamespace: {
		url: 'https://262.ecma-international.org/12.0/#sec-getmodulenamespace'
	},
	GetNewTarget: {
		url: 'https://262.ecma-international.org/12.0/#sec-getnewtarget'
	},
	GetOwnPropertyKeys: {
		url: 'https://262.ecma-international.org/12.0/#sec-getownpropertykeys'
	},
	GetPromiseResolve: {
		url: 'https://262.ecma-international.org/12.0/#sec-getpromiseresolve'
	},
	GetPrototypeFromConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-getprototypefromconstructor'
	},
	GetSubstitution: {
		url: 'https://262.ecma-international.org/12.0/#sec-getsubstitution'
	},
	GetSuperConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-getsuperconstructor'
	},
	GetTemplateObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-gettemplateobject'
	},
	GetThisEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-getthisenvironment'
	},
	GetThisValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-getthisvalue'
	},
	GetV: {
		url: 'https://262.ecma-international.org/12.0/#sec-getv'
	},
	GetValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-getvalue'
	},
	GetValueFromBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-getvaluefrombuffer'
	},
	GetViewValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-getviewvalue'
	},
	GetWaiterList: {
		url: 'https://262.ecma-international.org/12.0/#sec-getwaiterlist'
	},
	GlobalDeclarationInstantiation: {
		url: 'https://262.ecma-international.org/12.0/#sec-globaldeclarationinstantiation'
	},
	'happens-before': {
		url: 'https://262.ecma-international.org/12.0/#sec-happens-before'
	},
	HasOwnProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-hasownproperty'
	},
	HasProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-hasproperty'
	},
	'host-synchronizes-with': {
		url: 'https://262.ecma-international.org/12.0/#sec-host-synchronizes-with'
	},
	HostEventSet: {
		url: 'https://262.ecma-international.org/12.0/#sec-hosteventset'
	},
	HourFromTime: {
		url: 'https://262.ecma-international.org/12.0/#eqn-HourFromTime'
	},
	IfAbruptRejectPromise: {
		url: 'https://262.ecma-international.org/12.0/#sec-ifabruptrejectpromise'
	},
	ImportedLocalNames: {
		url: 'https://262.ecma-international.org/12.0/#sec-importedlocalnames'
	},
	InitializeBoundName: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializeboundname'
	},
	InitializeEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-source-text-module-record-initialize-environment'
	},
	InitializeHostDefinedRealm: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializehostdefinedrealm'
	},
	InitializeReferencedBinding: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializereferencedbinding'
	},
	InitializeTypedArrayFromArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializetypedarrayfromarraybuffer'
	},
	InitializeTypedArrayFromArrayLike: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializetypedarrayfromarraylike'
	},
	InitializeTypedArrayFromList: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializetypedarrayfromlist'
	},
	InitializeTypedArrayFromTypedArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-initializetypedarrayfromtypedarray'
	},
	InLeapYear: {
		url: 'https://262.ecma-international.org/12.0/#eqn-InLeapYear'
	},
	InnerModuleEvaluation: {
		url: 'https://262.ecma-international.org/12.0/#sec-innermoduleevaluation'
	},
	InnerModuleLinking: {
		url: 'https://262.ecma-international.org/12.0/#sec-InnerModuleLinking'
	},
	InstanceofOperator: {
		url: 'https://262.ecma-international.org/12.0/#sec-instanceofoperator'
	},
	IntegerIndexedElementGet: {
		url: 'https://262.ecma-international.org/12.0/#sec-integerindexedelementget'
	},
	IntegerIndexedElementSet: {
		url: 'https://262.ecma-international.org/12.0/#sec-integerindexedelementset'
	},
	IntegerIndexedObjectCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-integerindexedobjectcreate'
	},
	InternalizeJSONProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-internalizejsonproperty'
	},
	Invoke: {
		url: 'https://262.ecma-international.org/12.0/#sec-invoke'
	},
	IsAccessorDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-isaccessordescriptor'
	},
	IsAnonymousFunctionDefinition: {
		url: 'https://262.ecma-international.org/12.0/#sec-isanonymousfunctiondefinition'
	},
	IsArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-isarray'
	},
	IsBigIntElementType: {
		url: 'https://262.ecma-international.org/12.0/#sec-isbigintelementtype'
	},
	IsCallable: {
		url: 'https://262.ecma-international.org/12.0/#sec-iscallable'
	},
	IsCompatiblePropertyDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-iscompatiblepropertydescriptor'
	},
	IsConcatSpreadable: {
		url: 'https://262.ecma-international.org/12.0/#sec-isconcatspreadable'
	},
	IsConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-isconstructor'
	},
	IsDataDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-isdatadescriptor'
	},
	IsDetachedBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-isdetachedbuffer'
	},
	IsExtensible: {
		url: 'https://262.ecma-international.org/12.0/#sec-isextensible-o'
	},
	IsGenericDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-isgenericdescriptor'
	},
	IsInTailPosition: {
		url: 'https://262.ecma-international.org/12.0/#sec-isintailposition'
	},
	IsIntegralNumber: {
		url: 'https://262.ecma-international.org/12.0/#sec-isintegralnumber'
	},
	IsLabelledFunction: {
		url: 'https://262.ecma-international.org/12.0/#sec-islabelledfunction'
	},
	IsNoTearConfiguration: {
		url: 'https://262.ecma-international.org/12.0/#sec-isnotearconfiguration'
	},
	IsPromise: {
		url: 'https://262.ecma-international.org/12.0/#sec-ispromise'
	},
	IsPropertyKey: {
		url: 'https://262.ecma-international.org/12.0/#sec-ispropertykey'
	},
	IsPropertyReference: {
		url: 'https://262.ecma-international.org/12.0/#sec-ispropertyreference'
	},
	IsRegExp: {
		url: 'https://262.ecma-international.org/12.0/#sec-isregexp'
	},
	IsSharedArrayBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-issharedarraybuffer'
	},
	IsStringPrefix: {
		url: 'https://262.ecma-international.org/12.0/#sec-isstringprefix'
	},
	IsSuperReference: {
		url: 'https://262.ecma-international.org/12.0/#sec-issuperreference'
	},
	IsUnclampedIntegerElementType: {
		url: 'https://262.ecma-international.org/12.0/#sec-isunclampedintegerelementtype'
	},
	IsUnresolvableReference: {
		url: 'https://262.ecma-international.org/12.0/#sec-isunresolvablereference'
	},
	IsUnsignedElementType: {
		url: 'https://262.ecma-international.org/12.0/#sec-isunsignedelementtype'
	},
	IsValidIntegerIndex: {
		url: 'https://262.ecma-international.org/12.0/#sec-isvalidintegerindex'
	},
	IsValidRegularExpressionLiteral: {
		url: 'https://262.ecma-international.org/12.0/#sec-isvalidregularexpressionliteral'
	},
	IsWordChar: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-iswordchar-abstract-operation'
	},
	IterableToList: {
		url: 'https://262.ecma-international.org/12.0/#sec-iterabletolist'
	},
	IteratorClose: {
		url: 'https://262.ecma-international.org/12.0/#sec-iteratorclose'
	},
	IteratorComplete: {
		url: 'https://262.ecma-international.org/12.0/#sec-iteratorcomplete'
	},
	IteratorNext: {
		url: 'https://262.ecma-international.org/12.0/#sec-iteratornext'
	},
	IteratorStep: {
		url: 'https://262.ecma-international.org/12.0/#sec-iteratorstep'
	},
	IteratorValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-iteratorvalue'
	},
	LeaveCriticalSection: {
		url: 'https://262.ecma-international.org/12.0/#sec-leavecriticalsection'
	},
	LengthOfArrayLike: {
		url: 'https://262.ecma-international.org/12.0/#sec-lengthofarraylike'
	},
	LocalTime: {
		url: 'https://262.ecma-international.org/12.0/#sec-localtime'
	},
	LocalTZA: {
		url: 'https://262.ecma-international.org/12.0/#sec-local-time-zone-adjustment'
	},
	LoopContinues: {
		url: 'https://262.ecma-international.org/12.0/#sec-loopcontinues'
	},
	MakeArgGetter: {
		url: 'https://262.ecma-international.org/12.0/#sec-makearggetter'
	},
	MakeArgSetter: {
		url: 'https://262.ecma-international.org/12.0/#sec-makeargsetter'
	},
	MakeBasicObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-makebasicobject'
	},
	MakeClassConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-makeclassconstructor'
	},
	MakeConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-makeconstructor'
	},
	MakeDate: {
		url: 'https://262.ecma-international.org/12.0/#sec-makedate'
	},
	MakeDay: {
		url: 'https://262.ecma-international.org/12.0/#sec-makeday'
	},
	MakeMethod: {
		url: 'https://262.ecma-international.org/12.0/#sec-makemethod'
	},
	MakeSuperPropertyReference: {
		url: 'https://262.ecma-international.org/12.0/#sec-makesuperpropertyreference'
	},
	MakeTime: {
		url: 'https://262.ecma-international.org/12.0/#sec-maketime'
	},
	max: {
		url: 'https://262.ecma-international.org/12.0/#eqn-max'
	},
	'memory-order': {
		url: 'https://262.ecma-international.org/12.0/#sec-memory-order'
	},
	min: {
		url: 'https://262.ecma-international.org/12.0/#eqn-min'
	},
	MinFromTime: {
		url: 'https://262.ecma-international.org/12.0/#eqn-MinFromTime'
	},
	ModuleNamespaceCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-modulenamespacecreate'
	},
	modulo: {
		url: 'https://262.ecma-international.org/12.0/#eqn-modulo'
	},
	MonthFromTime: {
		url: 'https://262.ecma-international.org/12.0/#eqn-MonthFromTime'
	},
	msFromTime: {
		url: 'https://262.ecma-international.org/12.0/#eqn-msFromTime'
	},
	NewDeclarativeEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-newdeclarativeenvironment'
	},
	NewFunctionEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-newfunctionenvironment'
	},
	NewGlobalEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-newglobalenvironment'
	},
	NewModuleEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-newmoduleenvironment'
	},
	NewObjectEnvironment: {
		url: 'https://262.ecma-international.org/12.0/#sec-newobjectenvironment'
	},
	NewPromiseCapability: {
		url: 'https://262.ecma-international.org/12.0/#sec-newpromisecapability'
	},
	NewPromiseReactionJob: {
		url: 'https://262.ecma-international.org/12.0/#sec-newpromisereactionjob'
	},
	NewPromiseResolveThenableJob: {
		url: 'https://262.ecma-international.org/12.0/#sec-newpromiseresolvethenablejob'
	},
	NormalCompletion: {
		url: 'https://262.ecma-international.org/12.0/#sec-normalcompletion'
	},
	NotifyWaiter: {
		url: 'https://262.ecma-international.org/12.0/#sec-notifywaiter'
	},
	'Number::add': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-add'
	},
	'Number::bitwiseAND': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-bitwiseAND'
	},
	'Number::bitwiseNOT': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-bitwiseNOT'
	},
	'Number::bitwiseOR': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-bitwiseOR'
	},
	'Number::bitwiseXOR': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-bitwiseXOR'
	},
	'Number::divide': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-divide'
	},
	'Number::equal': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-equal'
	},
	'Number::exponentiate': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-exponentiate'
	},
	'Number::leftShift': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-leftShift'
	},
	'Number::lessThan': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-lessThan'
	},
	'Number::multiply': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-multiply'
	},
	'Number::remainder': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-remainder'
	},
	'Number::sameValue': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-sameValue'
	},
	'Number::sameValueZero': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-sameValueZero'
	},
	'Number::signedRightShift': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-signedRightShift'
	},
	'Number::subtract': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-subtract'
	},
	'Number::toString': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-tostring'
	},
	'Number::unaryMinus': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-unaryMinus'
	},
	'Number::unsignedRightShift': {
		url: 'https://262.ecma-international.org/12.0/#sec-numeric-types-number-unsignedRightShift'
	},
	NumberBitwiseOp: {
		url: 'https://262.ecma-international.org/12.0/#sec-numberbitwiseop'
	},
	NumberToBigInt: {
		url: 'https://262.ecma-international.org/12.0/#sec-numbertobigint'
	},
	NumericToRawBytes: {
		url: 'https://262.ecma-international.org/12.0/#sec-numerictorawbytes'
	},
	ObjectDefineProperties: {
		url: 'https://262.ecma-international.org/12.0/#sec-objectdefineproperties'
	},
	OrdinaryCallBindThis: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarycallbindthis'
	},
	OrdinaryCallEvaluateBody: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarycallevaluatebody'
	},
	OrdinaryCreateFromConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarycreatefromconstructor'
	},
	OrdinaryDefineOwnProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarydefineownproperty'
	},
	OrdinaryDelete: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarydelete'
	},
	OrdinaryFunctionCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryfunctioncreate'
	},
	OrdinaryGet: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryget'
	},
	OrdinaryGetOwnProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarygetownproperty'
	},
	OrdinaryGetPrototypeOf: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarygetprototypeof'
	},
	OrdinaryHasInstance: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryhasinstance'
	},
	OrdinaryHasProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryhasproperty'
	},
	OrdinaryIsExtensible: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryisextensible'
	},
	OrdinaryObjectCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryobjectcreate'
	},
	OrdinaryOwnPropertyKeys: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryownpropertykeys'
	},
	OrdinaryPreventExtensions: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarypreventextensions'
	},
	OrdinarySet: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinaryset'
	},
	OrdinarySetPrototypeOf: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarysetprototypeof'
	},
	OrdinarySetWithOwnDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarysetwithowndescriptor'
	},
	OrdinaryToPrimitive: {
		url: 'https://262.ecma-international.org/12.0/#sec-ordinarytoprimitive'
	},
	ParseModule: {
		url: 'https://262.ecma-international.org/12.0/#sec-parsemodule'
	},
	ParsePattern: {
		url: 'https://262.ecma-international.org/12.0/#sec-parsepattern'
	},
	ParseScript: {
		url: 'https://262.ecma-international.org/12.0/#sec-parse-script'
	},
	ParseText: {
		url: 'https://262.ecma-international.org/12.0/#sec-parsetext'
	},
	PerformEval: {
		url: 'https://262.ecma-international.org/12.0/#sec-performeval'
	},
	PerformPromiseAll: {
		url: 'https://262.ecma-international.org/12.0/#sec-performpromiseall'
	},
	PerformPromiseAllSettled: {
		url: 'https://262.ecma-international.org/12.0/#sec-performpromiseallsettled'
	},
	PerformPromiseAny: {
		url: 'https://262.ecma-international.org/12.0/#sec-performpromiseany'
	},
	PerformPromiseRace: {
		url: 'https://262.ecma-international.org/12.0/#sec-performpromiserace'
	},
	PerformPromiseThen: {
		url: 'https://262.ecma-international.org/12.0/#sec-performpromisethen'
	},
	PrepareForOrdinaryCall: {
		url: 'https://262.ecma-international.org/12.0/#sec-prepareforordinarycall'
	},
	PrepareForTailCall: {
		url: 'https://262.ecma-international.org/12.0/#sec-preparefortailcall'
	},
	PromiseResolve: {
		url: 'https://262.ecma-international.org/12.0/#sec-promise-resolve'
	},
	ProxyCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-proxycreate'
	},
	PutValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-putvalue'
	},
	QuoteJSONString: {
		url: 'https://262.ecma-international.org/12.0/#sec-quotejsonstring'
	},
	RawBytesToNumeric: {
		url: 'https://262.ecma-international.org/12.0/#sec-rawbytestonumeric'
	},
	'reads-bytes-from': {
		url: 'https://262.ecma-international.org/12.0/#sec-reads-bytes-from'
	},
	'reads-from': {
		url: 'https://262.ecma-international.org/12.0/#sec-reads-from'
	},
	RegExpAlloc: {
		url: 'https://262.ecma-international.org/12.0/#sec-regexpalloc'
	},
	RegExpBuiltinExec: {
		url: 'https://262.ecma-international.org/12.0/#sec-regexpbuiltinexec'
	},
	RegExpCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-regexpcreate'
	},
	RegExpExec: {
		url: 'https://262.ecma-international.org/12.0/#sec-regexpexec'
	},
	RegExpInitialize: {
		url: 'https://262.ecma-international.org/12.0/#sec-regexpinitialize'
	},
	RejectPromise: {
		url: 'https://262.ecma-international.org/12.0/#sec-rejectpromise'
	},
	RemoveWaiter: {
		url: 'https://262.ecma-international.org/12.0/#sec-removewaiter'
	},
	RemoveWaiters: {
		url: 'https://262.ecma-international.org/12.0/#sec-removewaiters'
	},
	RepeatMatcher: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-repeatmatcher-abstract-operation'
	},
	RequireInternalSlot: {
		url: 'https://262.ecma-international.org/12.0/#sec-requireinternalslot'
	},
	RequireObjectCoercible: {
		url: 'https://262.ecma-international.org/12.0/#sec-requireobjectcoercible'
	},
	ResolveBinding: {
		url: 'https://262.ecma-international.org/12.0/#sec-resolvebinding'
	},
	ResolveThisBinding: {
		url: 'https://262.ecma-international.org/12.0/#sec-resolvethisbinding'
	},
	ReturnIfAbrupt: {
		url: 'https://262.ecma-international.org/12.0/#sec-returnifabrupt'
	},
	SameValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-samevalue'
	},
	SameValueNonNumeric: {
		url: 'https://262.ecma-international.org/12.0/#sec-samevaluenonnumeric'
	},
	SameValueZero: {
		url: 'https://262.ecma-international.org/12.0/#sec-samevaluezero'
	},
	ScriptEvaluation: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-scriptevaluation'
	},
	SecFromTime: {
		url: 'https://262.ecma-international.org/12.0/#eqn-SecFromTime'
	},
	SerializeJSONArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-serializejsonarray'
	},
	SerializeJSONObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-serializejsonobject'
	},
	SerializeJSONProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-serializejsonproperty'
	},
	Set: {
		url: 'https://262.ecma-international.org/12.0/#sec-set-o-p-v-throw'
	},
	SetDefaultGlobalBindings: {
		url: 'https://262.ecma-international.org/12.0/#sec-setdefaultglobalbindings'
	},
	SetFunctionLength: {
		url: 'https://262.ecma-international.org/12.0/#sec-setfunctionlength'
	},
	SetFunctionName: {
		url: 'https://262.ecma-international.org/12.0/#sec-setfunctionname'
	},
	SetImmutablePrototype: {
		url: 'https://262.ecma-international.org/12.0/#sec-set-immutable-prototype'
	},
	SetIntegrityLevel: {
		url: 'https://262.ecma-international.org/12.0/#sec-setintegritylevel'
	},
	SetRealmGlobalObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-setrealmglobalobject'
	},
	SetTypedArrayFromArrayLike: {
		url: 'https://262.ecma-international.org/12.0/#sec-settypedarrayfromarraylike'
	},
	SetTypedArrayFromTypedArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-settypedarrayfromtypedarray'
	},
	SetValueInBuffer: {
		url: 'https://262.ecma-international.org/12.0/#sec-setvalueinbuffer'
	},
	SetViewValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-setviewvalue'
	},
	SharedDataBlockEventSet: {
		url: 'https://262.ecma-international.org/12.0/#sec-sharedatablockeventset'
	},
	SortCompare: {
		url: 'https://262.ecma-international.org/12.0/#sec-sortcompare'
	},
	SpeciesConstructor: {
		url: 'https://262.ecma-international.org/12.0/#sec-speciesconstructor'
	},
	SplitMatch: {
		url: 'https://262.ecma-international.org/12.0/#sec-splitmatch'
	},
	'Strict Equality Comparison': {
		url: 'https://262.ecma-international.org/12.0/#sec-strict-equality-comparison'
	},
	StringCreate: {
		url: 'https://262.ecma-international.org/12.0/#sec-stringcreate'
	},
	StringGetOwnProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-stringgetownproperty'
	},
	StringIndexOf: {
		url: 'https://262.ecma-international.org/12.0/#sec-stringindexof'
	},
	StringPad: {
		url: 'https://262.ecma-international.org/12.0/#sec-stringpad'
	},
	StringToBigInt: {
		url: 'https://262.ecma-international.org/12.0/#sec-stringtobigint'
	},
	StringToCodePoints: {
		url: 'https://262.ecma-international.org/12.0/#sec-stringtocodepoints'
	},
	substring: {
		url: 'https://262.ecma-international.org/12.0/#substring'
	},
	SuspendAgent: {
		url: 'https://262.ecma-international.org/12.0/#sec-suspendagent'
	},
	SymbolDescriptiveString: {
		url: 'https://262.ecma-international.org/12.0/#sec-symboldescriptivestring'
	},
	'synchronizes-with': {
		url: 'https://262.ecma-international.org/12.0/#sec-synchronizes-with'
	},
	TestIntegrityLevel: {
		url: 'https://262.ecma-international.org/12.0/#sec-testintegritylevel'
	},
	thisBigIntValue: {
		url: 'https://262.ecma-international.org/12.0/#thisbigintvalue'
	},
	thisBooleanValue: {
		url: 'https://262.ecma-international.org/12.0/#thisbooleanvalue'
	},
	thisNumberValue: {
		url: 'https://262.ecma-international.org/12.0/#thisnumbervalue'
	},
	thisStringValue: {
		url: 'https://262.ecma-international.org/12.0/#thisstringvalue'
	},
	thisSymbolValue: {
		url: 'https://262.ecma-international.org/12.0/#thissymbolvalue'
	},
	thisTimeValue: {
		url: 'https://262.ecma-international.org/12.0/#thistimevalue'
	},
	ThrowCompletion: {
		url: 'https://262.ecma-international.org/12.0/#sec-throwcompletion'
	},
	TimeClip: {
		url: 'https://262.ecma-international.org/12.0/#sec-timeclip'
	},
	TimeFromYear: {
		url: 'https://262.ecma-international.org/12.0/#eqn-TimeFromYear'
	},
	TimeString: {
		url: 'https://262.ecma-international.org/12.0/#sec-timestring'
	},
	TimeWithinDay: {
		url: 'https://262.ecma-international.org/12.0/#eqn-TimeWithinDay'
	},
	TimeZoneString: {
		url: 'https://262.ecma-international.org/12.0/#sec-timezoneestring'
	},
	ToBigInt: {
		url: 'https://262.ecma-international.org/12.0/#sec-tobigint'
	},
	ToBigInt64: {
		url: 'https://262.ecma-international.org/12.0/#sec-tobigint64'
	},
	ToBigUint64: {
		url: 'https://262.ecma-international.org/12.0/#sec-tobiguint64'
	},
	ToBoolean: {
		url: 'https://262.ecma-international.org/12.0/#sec-toboolean'
	},
	ToDateString: {
		url: 'https://262.ecma-international.org/12.0/#sec-todatestring'
	},
	ToIndex: {
		url: 'https://262.ecma-international.org/12.0/#sec-toindex'
	},
	ToInt16: {
		url: 'https://262.ecma-international.org/12.0/#sec-toint16'
	},
	ToInt32: {
		url: 'https://262.ecma-international.org/12.0/#sec-toint32'
	},
	ToInt8: {
		url: 'https://262.ecma-international.org/12.0/#sec-toint8'
	},
	ToIntegerOrInfinity: {
		url: 'https://262.ecma-international.org/12.0/#sec-tointegerorinfinity'
	},
	ToLength: {
		url: 'https://262.ecma-international.org/12.0/#sec-tolength'
	},
	ToNumber: {
		url: 'https://262.ecma-international.org/12.0/#sec-tonumber'
	},
	ToNumeric: {
		url: 'https://262.ecma-international.org/12.0/#sec-tonumeric'
	},
	ToObject: {
		url: 'https://262.ecma-international.org/12.0/#sec-toobject'
	},
	ToPrimitive: {
		url: 'https://262.ecma-international.org/12.0/#sec-toprimitive'
	},
	ToPropertyDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-topropertydescriptor'
	},
	ToPropertyKey: {
		url: 'https://262.ecma-international.org/12.0/#sec-topropertykey'
	},
	ToString: {
		url: 'https://262.ecma-international.org/12.0/#sec-tostring'
	},
	ToUint16: {
		url: 'https://262.ecma-international.org/12.0/#sec-touint16'
	},
	ToUint32: {
		url: 'https://262.ecma-international.org/12.0/#sec-touint32'
	},
	ToUint8: {
		url: 'https://262.ecma-international.org/12.0/#sec-touint8'
	},
	ToUint8Clamp: {
		url: 'https://262.ecma-international.org/12.0/#sec-touint8clamp'
	},
	TriggerPromiseReactions: {
		url: 'https://262.ecma-international.org/12.0/#sec-triggerpromisereactions'
	},
	TrimString: {
		url: 'https://262.ecma-international.org/12.0/#sec-trimstring'
	},
	Type: {
		url: 'https://262.ecma-international.org/12.0/#sec-ecmascript-data-types-and-values'
	},
	TypedArrayCreate: {
		url: 'https://262.ecma-international.org/12.0/#typedarray-create'
	},
	TypedArraySpeciesCreate: {
		url: 'https://262.ecma-international.org/12.0/#typedarray-species-create'
	},
	UnicodeEscape: {
		url: 'https://262.ecma-international.org/12.0/#sec-unicodeescape'
	},
	UnicodeMatchProperty: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-unicodematchproperty-p'
	},
	UnicodeMatchPropertyValue: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-unicodematchpropertyvalue-p-v'
	},
	UpdateEmpty: {
		url: 'https://262.ecma-international.org/12.0/#sec-updateempty'
	},
	UTC: {
		url: 'https://262.ecma-international.org/12.0/#sec-utc-t'
	},
	UTF16EncodeCodePoint: {
		url: 'https://262.ecma-international.org/12.0/#sec-utf16encodecodepoint'
	},
	UTF16SurrogatePairToCodePoint: {
		url: 'https://262.ecma-international.org/12.0/#sec-utf16decodesurrogatepair'
	},
	ValidateAndApplyPropertyDescriptor: {
		url: 'https://262.ecma-international.org/12.0/#sec-validateandapplypropertydescriptor'
	},
	ValidateAtomicAccess: {
		url: 'https://262.ecma-international.org/12.0/#sec-validateatomicaccess'
	},
	ValidateIntegerTypedArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-validateintegertypedarray'
	},
	ValidateTypedArray: {
		url: 'https://262.ecma-international.org/12.0/#sec-validatetypedarray'
	},
	ValueOfReadEvent: {
		url: 'https://262.ecma-international.org/12.0/#sec-valueofreadevent'
	},
	WeakRefDeref: {
		url: 'https://262.ecma-international.org/12.0/#sec-weakrefderef'
	},
	WeekDay: {
		url: 'https://262.ecma-international.org/12.0/#sec-week-day'
	},
	WordCharacters: {
		url: 'https://262.ecma-international.org/12.0/#sec-runtime-semantics-wordcharacters-abstract-operation'
	},
	YearFromTime: {
		url: 'https://262.ecma-international.org/12.0/#eqn-YearFromTime'
	},
	Yield: {
		url: 'https://262.ecma-international.org/12.0/#sec-yield'
	}
};
