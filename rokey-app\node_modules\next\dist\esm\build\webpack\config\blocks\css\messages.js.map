{"version": 3, "sources": ["../../../../../../src/build/webpack/config/blocks/css/messages.ts"], "sourcesContent": ["import { bold, cyan } from '../../../../../lib/picocolors'\n\nexport function getGlobalImportError() {\n  return `Global CSS ${bold(\n    'cannot'\n  )} be imported from files other than your ${bold(\n    'Custom <App>'\n  )}. Due to the Global nature of stylesheets, and to avoid conflicts, Please move all first-party global CSS imports to ${cyan(\n    'pages/_app.js'\n  )}. Or convert the import to Component-Level CSS (CSS Modules).\\nRead more: https://nextjs.org/docs/messages/css-global`\n}\n\nexport function getGlobalModuleImportError() {\n  return `Global CSS ${bold('cannot')} be imported from within ${bold(\n    'node_modules'\n  )}.\\nRead more: https://nextjs.org/docs/messages/css-npm`\n}\n\nexport function getLocalModuleImportError() {\n  return `CSS Modules ${bold('cannot')} be imported from within ${bold(\n    'node_modules'\n  )}.\\nRead more: https://nextjs.org/docs/messages/css-modules-npm`\n}\n\nexport function getCustomDocumentError() {\n  return `CSS ${bold('cannot')} be imported within ${cyan(\n    'pages/_document.js'\n  )}. Please move global styles to ${cyan('pages/_app.js')}.`\n}\n"], "names": ["bold", "cyan", "getGlobalImportError", "getGlobalModuleImportError", "getLocalModuleImportError", "getCustomDocumentError"], "mappings": "AAAA,SAASA,IAAI,EAAEC,IAAI,QAAQ,gCAA+B;AAE1D,OAAO,SAASC;IACd,OAAO,CAAC,WAAW,EAAEF,KACnB,UACA,wCAAwC,EAAEA,KAC1C,gBACA,qHAAqH,EAAEC,KACvH,iBACA,qHAAqH,CAAC;AAC1H;AAEA,OAAO,SAASE;IACd,OAAO,CAAC,WAAW,EAAEH,KAAK,UAAU,yBAAyB,EAAEA,KAC7D,gBACA,sDAAsD,CAAC;AAC3D;AAEA,OAAO,SAASI;IACd,OAAO,CAAC,YAAY,EAAEJ,KAAK,UAAU,yBAAyB,EAAEA,KAC9D,gBACA,8DAA8D,CAAC;AACnE;AAEA,OAAO,SAASK;IACd,OAAO,CAAC,IAAI,EAAEL,KAAK,UAAU,oBAAoB,EAAEC,KACjD,sBACA,+BAA+B,EAAEA,KAAK,iBAAiB,CAAC,CAAC;AAC7D"}