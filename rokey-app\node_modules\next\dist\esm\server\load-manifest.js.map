{"version": 3, "sources": ["../../src/server/load-manifest.ts"], "sourcesContent": ["import type { DeepReadonly } from '../shared/lib/deep-readonly'\n\nimport { readFileSync } from 'fs'\nimport { runInNewContext } from 'vm'\nimport { deepFreeze } from '../shared/lib/deep-freeze'\n\nconst sharedCache = new Map<string, unknown>()\n\n/**\n * Load a manifest file from the file system. Optionally cache the manifest in\n * memory to avoid reading the file multiple times using the provided cache or\n * defaulting to a shared module cache. The manifest is frozen to prevent\n * modifications if it is cached.\n *\n * @param path the path to the manifest file\n * @param shouldCache whether to cache the manifest in memory\n * @param cache the cache to use for storing the manifest\n * @returns the manifest object\n */\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache: false\n): T\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache?: boolean,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache?: true,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function loadManifest<T extends object>(\n  path: string,\n  shouldCache: boolean = true,\n  cache = sharedCache\n): T {\n  const cached = shouldCache && cache.get(path)\n  if (cached) {\n    return cached as T\n  }\n\n  let manifest = JSON.parse(readFileSync(path, 'utf8'))\n\n  // Freeze the manifest so it cannot be modified if we're caching it.\n  if (shouldCache) {\n    manifest = deepFreeze(manifest)\n  }\n\n  if (shouldCache) {\n    cache.set(path, manifest)\n  }\n\n  return manifest\n}\n\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache: false\n): T\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache?: boolean,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache?: true,\n  cache?: Map<string, unknown>\n): DeepReadonly<T>\nexport function evalManifest<T extends object>(\n  path: string,\n  shouldCache: boolean = true,\n  cache = sharedCache\n): T {\n  const cached = shouldCache && cache.get(path)\n  if (cached) {\n    return cached as T\n  }\n\n  const content = readFileSync(path, 'utf8')\n  if (content.length === 0) {\n    throw new Error('Manifest file is empty')\n  }\n\n  let contextObject = {}\n  runInNewContext(content, contextObject)\n\n  // Freeze the context object so it cannot be modified if we're caching it.\n  if (shouldCache) {\n    contextObject = deepFreeze(contextObject)\n  }\n\n  if (shouldCache) {\n    cache.set(path, contextObject)\n  }\n\n  return contextObject as T\n}\n\nexport function clearManifestCache(path: string, cache = sharedCache): boolean {\n  return cache.delete(path)\n}\n"], "names": ["readFileSync", "runInNewContext", "deepFreeze", "sharedCache", "Map", "loadManifest", "path", "shouldCache", "cache", "cached", "get", "manifest", "JSON", "parse", "set", "evalManifest", "content", "length", "Error", "contextObject", "clearManifestCache", "delete"], "mappings": "AAEA,SAASA,YAAY,QAAQ,KAAI;AACjC,SAASC,eAAe,QAAQ,KAAI;AACpC,SAASC,UAAU,QAAQ,4BAA2B;AAEtD,MAAMC,cAAc,IAAIC;AA2BxB,OAAO,SAASC,aACdC,IAAY,EACZC,cAAuB,IAAI,EAC3BC,QAAQL,WAAW;IAEnB,MAAMM,SAASF,eAAeC,MAAME,GAAG,CAACJ;IACxC,IAAIG,QAAQ;QACV,OAAOA;IACT;IAEA,IAAIE,WAAWC,KAAKC,KAAK,CAACb,aAAaM,MAAM;IAE7C,oEAAoE;IACpE,IAAIC,aAAa;QACfI,WAAWT,WAAWS;IACxB;IAEA,IAAIJ,aAAa;QACfC,MAAMM,GAAG,CAACR,MAAMK;IAClB;IAEA,OAAOA;AACT;AAgBA,OAAO,SAASI,aACdT,IAAY,EACZC,cAAuB,IAAI,EAC3BC,QAAQL,WAAW;IAEnB,MAAMM,SAASF,eAAeC,MAAME,GAAG,CAACJ;IACxC,IAAIG,QAAQ;QACV,OAAOA;IACT;IAEA,MAAMO,UAAUhB,aAAaM,MAAM;IACnC,IAAIU,QAAQC,MAAM,KAAK,GAAG;QACxB,MAAM,qBAAmC,CAAnC,IAAIC,MAAM,2BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAkC;IAC1C;IAEA,IAAIC,gBAAgB,CAAC;IACrBlB,gBAAgBe,SAASG;IAEzB,0EAA0E;IAC1E,IAAIZ,aAAa;QACfY,gBAAgBjB,WAAWiB;IAC7B;IAEA,IAAIZ,aAAa;QACfC,MAAMM,GAAG,CAACR,MAAMa;IAClB;IAEA,OAAOA;AACT;AAEA,OAAO,SAASC,mBAAmBd,IAAY,EAAEE,QAAQL,WAAW;IAClE,OAAOK,MAAMa,MAAM,CAACf;AACtB"}