import { deepCopy, uuid6, } from "@langchain/langgraph-checkpoint";
import { EmptyChannelError } from "../errors.js";
export function isBaseChannel(obj) {
    return obj != null && obj.lg_is_channel === true;
}
export class BaseChannel {
    constructor() {
        Object.defineProperty(this, "ValueType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        Object.defineProperty(this, "UpdateType", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        /** @ignore */
        Object.defineProperty(this, "lg_is_channel", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: true
        });
    }
    /**
     * Mark the current value of the channel as consumed. By default, no-op.
     * A channel can use this method to modify its state, preventing the value
     * from being consumed again.
     *
     * Returns True if the channel was updated, False otherwise.
     */
    consume() {
        return false;
    }
    /**
     * Notify the channel that the Pregel run is finishing. By default, no-op.
     * A channel can use this method to modify its state, preventing finish.
     *
     * Returns True if the channel was updated, False otherwise.
     */
    finish() {
        return false;
    }
    /**
     * Return True if the channel is available (not empty), False otherwise.
     * Subclasses should override this method to provide a more efficient
     * implementation than calling get() and catching EmptyChannelError.
     */
    isAvailable() {
        try {
            this.get();
            return true;
            // eslint-disable-next-line @typescript-eslint/no-explicit-any
        }
        catch (error) {
            if (error.name === EmptyChannelError.unminifiable_name) {
                return false;
            }
            throw error;
        }
    }
}
export function emptyChannels(channels, checkpoint) {
    const filteredChannels = Object.fromEntries(Object.entries(channels).filter(([, value]) => isBaseChannel(value)));
    const newChannels = {};
    for (const k in filteredChannels) {
        if (Object.prototype.hasOwnProperty.call(filteredChannels, k)) {
            const channelValue = checkpoint.channel_values[k];
            newChannels[k] = filteredChannels[k].fromCheckpoint(channelValue);
        }
    }
    return newChannels;
}
export function createCheckpoint(checkpoint, channels, step) {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    let values;
    if (channels === undefined) {
        values = checkpoint.channel_values;
    }
    else {
        values = {};
        for (const k of Object.keys(channels)) {
            try {
                values[k] = channels[k].checkpoint();
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
            }
            catch (error) {
                if (error.name === EmptyChannelError.unminifiable_name) {
                    // no-op
                }
                else {
                    throw error; // Rethrow unexpected errors
                }
            }
        }
    }
    return {
        v: 1,
        id: uuid6(step),
        ts: new Date().toISOString(),
        channel_values: values,
        channel_versions: { ...checkpoint.channel_versions },
        versions_seen: deepCopy(checkpoint.versions_seen),
        pending_sends: checkpoint.pending_sends ?? [],
    };
}
//# sourceMappingURL=base.js.map