"use strict";
/**
 * (C) Copyright IBM Corp. 2019, 2024.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNewLogger = exports.contentType = exports.qs = exports.BaseService = void 0;
/**
 * @module ibm-cloud-sdk-core
 */
__exportStar(require("./lib/axios-types"), exports);
var base_service_1 = require("./lib/base-service");
Object.defineProperty(exports, "BaseService", { enumerable: true, get: function () { return base_service_1.BaseService; } });
__exportStar(require("./auth"), exports);
__exportStar(require("./lib/helper"), exports);
var querystring_1 = require("./lib/querystring");
Object.defineProperty(exports, "qs", { enumerable: true, get: function () { return __importDefault(querystring_1).default; } });
var content_type_1 = require("./lib/content-type");
Object.defineProperty(exports, "contentType", { enumerable: true, get: function () { return __importDefault(content_type_1).default; } });
__exportStar(require("./lib/stream-to-promise"), exports);
var get_new_logger_1 = require("./lib/get-new-logger");
Object.defineProperty(exports, "getNewLogger", { enumerable: true, get: function () { return get_new_logger_1.getNewLogger; } });
