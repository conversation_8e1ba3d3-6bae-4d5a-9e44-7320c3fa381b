{"version": 3, "sources": ["../../../../src/experimental/testmode/playwright/msw.ts"], "sourcesContent": ["import { test as base, defineConfig } from './index'\nimport type { NextFixture } from './next-fixture'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { type RequestHandler, handleRequest } from 'msw'\n// eslint-disable-next-line import/no-extraneous-dependencies\nimport { Emitter } from 'strict-event-emitter'\n\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport * from 'msw'\n// eslint-disable-next-line import/no-extraneous-dependencies\nexport * from '@playwright/test'\nexport type { NextFixture }\nexport { defineConfig }\n\nexport interface MswFixture {\n  use: (...handlers: RequestHandler[]) => void\n}\n\nexport const test = base.extend<{\n  msw: MswFixture\n  mswHandlers: RequestHandler[]\n}>({\n  mswHandlers: [[], { option: true }],\n\n  msw: [\n    async ({ next, mswHandlers }, use) => {\n      const handlers: RequestHandler[] = [...mswHandlers]\n      const emitter = new Emitter()\n\n      next.onFetch(async (request) => {\n        const requestId = Math.random().toString(16).slice(2)\n        let isUnhandled = false\n        let isPassthrough = false\n        let mockedResponse\n        await handleRequest(\n          request.clone(),\n          requestId,\n          handlers.slice(0),\n          {\n            onUnhandledRequest: () => {\n              isUnhandled = true\n            },\n          },\n          emitter as any,\n          {\n            onPassthroughResponse: () => {\n              isPassthrough = true\n            },\n            onMockedResponse: (r) => {\n              mockedResponse = r\n            },\n          }\n        )\n\n        if (isUnhandled) {\n          return undefined\n        }\n        if (isPassthrough) {\n          return 'continue'\n        }\n\n        if (mockedResponse) {\n          return mockedResponse\n        }\n\n        return 'abort'\n      })\n\n      await use({\n        use: (...newHandlers) => {\n          handlers.unshift(...newHandlers)\n        },\n      })\n\n      handlers.length = 0\n    },\n    { auto: true },\n  ],\n})\n\nexport default test\n"], "names": ["defineConfig", "test", "base", "extend", "mswHandlers", "option", "msw", "next", "use", "handlers", "emitter", "Emitter", "onFetch", "request", "requestId", "Math", "random", "toString", "slice", "isUnhandled", "isPassthrough", "mockedResponse", "handleRequest", "clone", "onUnhandledRequest", "onPassthroughResponse", "onMockedResponse", "r", "undefined", "newHandlers", "unshift", "length", "auto"], "mappings": ";;;;;;;;;;;;;;;;IAgFA,OAAmB;eAAnB;;IApESA,YAAY;eAAZA,mBAAY;;IAMRC,IAAI;eAAJA;;;;uBAlB8B;kCAGQ;oCAE3B;qBAKV;;;;;;;;;;;;;;AAQP,MAAMA,OAAOC,WAAI,CAACC,MAAM,CAG5B;IACDC,aAAa;QAAC,EAAE;QAAE;YAAEC,QAAQ;QAAK;KAAE;IAEnCC,KAAK;QACH,OAAO,EAAEC,IAAI,EAAEH,WAAW,EAAE,EAAEI;YAC5B,MAAMC,WAA6B;mBAAIL;aAAY;YACnD,MAAMM,UAAU,IAAIC,2BAAO;YAE3BJ,KAAKK,OAAO,CAAC,OAAOC;gBAClB,MAAMC,YAAYC,KAAKC,MAAM,GAAGC,QAAQ,CAAC,IAAIC,KAAK,CAAC;gBACnD,IAAIC,cAAc;gBAClB,IAAIC,gBAAgB;gBACpB,IAAIC;gBACJ,MAAMC,IAAAA,kBAAa,EACjBT,QAAQU,KAAK,IACbT,WACAL,SAASS,KAAK,CAAC,IACf;oBACEM,oBAAoB;wBAClBL,cAAc;oBAChB;gBACF,GACAT,SACA;oBACEe,uBAAuB;wBACrBL,gBAAgB;oBAClB;oBACAM,kBAAkB,CAACC;wBACjBN,iBAAiBM;oBACnB;gBACF;gBAGF,IAAIR,aAAa;oBACf,OAAOS;gBACT;gBACA,IAAIR,eAAe;oBACjB,OAAO;gBACT;gBAEA,IAAIC,gBAAgB;oBAClB,OAAOA;gBACT;gBAEA,OAAO;YACT;YAEA,MAAMb,IAAI;gBACRA,KAAK,CAAC,GAAGqB;oBACPpB,SAASqB,OAAO,IAAID;gBACtB;YACF;YAEApB,SAASsB,MAAM,GAAG;QACpB;QACA;YAAEC,MAAM;QAAK;KACd;AACH;MAEA,WAAe/B"}