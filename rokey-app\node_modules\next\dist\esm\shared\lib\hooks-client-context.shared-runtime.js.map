{"version": 3, "sources": ["../../../src/shared/lib/hooks-client-context.shared-runtime.ts"], "sourcesContent": ["'use client'\n\nimport { createContext } from 'react'\nimport type { Params } from '../../server/request/params'\n\nexport const SearchParamsContext = createContext<URLSearchParams | null>(null)\nexport const PathnameContext = createContext<string | null>(null)\nexport const PathParamsContext = createContext<Params | null>(null)\n\nif (process.env.NODE_ENV !== 'production') {\n  SearchParamsContext.displayName = 'SearchParamsContext'\n  PathnameContext.displayName = 'PathnameContext'\n  PathParamsContext.displayName = 'PathParamsContext'\n}\n"], "names": ["createContext", "SearchParamsContext", "PathnameContext", "PathParamsContext", "process", "env", "NODE_ENV", "displayName"], "mappings": "AAAA;AAEA,SAASA,aAAa,QAAQ,QAAO;AAGrC,OAAO,MAAMC,sBAAsBD,cAAsC,MAAK;AAC9E,OAAO,MAAME,kBAAkBF,cAA6B,MAAK;AACjE,OAAO,MAAMG,oBAAoBH,cAA6B,MAAK;AAEnE,IAAII,QAAQC,GAAG,CAACC,QAAQ,KAAK,cAAc;IACzCL,oBAAoBM,WAAW,GAAG;IAClCL,gBAAgBK,WAAW,GAAG;IAC9BJ,kBAAkBI,WAAW,GAAG;AAClC"}