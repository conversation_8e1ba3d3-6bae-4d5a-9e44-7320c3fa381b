"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs-comment": {
    "color": "#776e71"
  },
  "hljs-quote": {
    "color": "#776e71"
  },
  "hljs-variable": {
    "color": "#ef6155"
  },
  "hljs-template-variable": {
    "color": "#ef6155"
  },
  "hljs-tag": {
    "color": "#ef6155"
  },
  "hljs-name": {
    "color": "#ef6155"
  },
  "hljs-selector-id": {
    "color": "#ef6155"
  },
  "hljs-selector-class": {
    "color": "#ef6155"
  },
  "hljs-regexp": {
    "color": "#ef6155"
  },
  "hljs-link": {
    "color": "#ef6155"
  },
  "hljs-meta": {
    "color": "#ef6155"
  },
  "hljs-number": {
    "color": "#f99b15"
  },
  "hljs-built_in": {
    "color": "#f99b15"
  },
  "hljs-builtin-name": {
    "color": "#f99b15"
  },
  "hljs-literal": {
    "color": "#f99b15"
  },
  "hljs-type": {
    "color": "#f99b15"
  },
  "hljs-params": {
    "color": "#f99b15"
  },
  "hljs-deletion": {
    "color": "#f99b15"
  },
  "hljs-title": {
    "color": "#fec418"
  },
  "hljs-section": {
    "color": "#fec418"
  },
  "hljs-attribute": {
    "color": "#fec418"
  },
  "hljs-string": {
    "color": "#48b685"
  },
  "hljs-symbol": {
    "color": "#48b685"
  },
  "hljs-bullet": {
    "color": "#48b685"
  },
  "hljs-addition": {
    "color": "#48b685"
  },
  "hljs-keyword": {
    "color": "#815ba4"
  },
  "hljs-selector-tag": {
    "color": "#815ba4"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "#e7e9db",
    "color": "#4f424c",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};