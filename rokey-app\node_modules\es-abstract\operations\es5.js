'use strict';

module.exports = {
	'Abstract Equality Comparison': 'https://262.ecma-international.org/5.1/#sec-11.9.3',
	'Abstract Relational Comparison': 'https://262.ecma-international.org/5.1/#sec-11.8.5',
	'Strict Equality Comparison': 'https://262.ecma-international.org/5.1/#sec-11.9.6',
	abs: 'http://262.ecma-international.org/5.1/#sec-5.2',
	Canonicalize: 'https://262.ecma-international.org/5.1/#sec-*********',
	CheckObjectCoercible: 'https://262.ecma-international.org/5.1/#sec-9.10',
	DateFromTime: 'https://262.ecma-international.org/5.1/#sec-********',
	Day: 'https://262.ecma-international.org/5.1/#sec-********',
	DayFromYear: 'https://262.ecma-international.org/5.1/#sec-********',
	DaysInYear: 'https://262.ecma-international.org/5.1/#sec-********',
	DayWithinYear: 'https://262.ecma-international.org/5.1/#sec-********',
	floor: 'http://262.ecma-international.org/5.1/#sec-5.2',
	FromPropertyDescriptor: 'https://262.ecma-international.org/5.1/#sec-8.10.4',
	HourFromTime: 'https://262.ecma-international.org/5.1/#sec-*********',
	InLeapYear: 'https://262.ecma-international.org/5.1/#sec-********',
	IsAccessorDescriptor: 'https://262.ecma-international.org/5.1/#sec-8.10.1',
	IsCallable: 'https://262.ecma-international.org/5.1/#sec-9.11',
	IsDataDescriptor: 'https://262.ecma-international.org/5.1/#sec-8.10.2',
	IsGenericDescriptor: 'https://262.ecma-international.org/5.1/#sec-8.10.3',
	IsPropertyDescriptor: 'https://262.ecma-international.org/5.1/#sec-8.10',
	MakeDate: 'https://262.ecma-international.org/5.1/#sec-*********',
	MakeDay: 'https://262.ecma-international.org/5.1/#sec-*********',
	MakeTime: 'https://262.ecma-international.org/5.1/#sec-*********',
	MinFromTime: 'https://262.ecma-international.org/5.1/#sec-*********',
	modulo: 'https://262.ecma-international.org/5.1/#sec-5.2',
	MonthFromTime: 'https://262.ecma-international.org/5.1/#sec-********',
	msFromTime: 'https://262.ecma-international.org/5.1/#sec-*********',
	SameValue: 'https://262.ecma-international.org/5.1/#sec-9.12',
	SecFromTime: 'https://262.ecma-international.org/5.1/#sec-*********',
	SplitMatch: 'https://262.ecma-international.org/5.1/#sec-*********',
	TimeClip: 'https://262.ecma-international.org/5.1/#sec-*********',
	TimeFromYear: 'https://262.ecma-international.org/5.1/#sec-********',
	TimeWithinDay: 'https://262.ecma-international.org/5.1/#sec-********',
	ToBoolean: 'https://262.ecma-international.org/5.1/#sec-9.2',
	ToInt32: 'https://262.ecma-international.org/5.1/#sec-9.5',
	ToInteger: 'https://262.ecma-international.org/5.1/#sec-9.4',
	ToNumber: 'https://262.ecma-international.org/5.1/#sec-9.3',
	ToObject: 'https://262.ecma-international.org/5.1/#sec-9.9',
	ToPrimitive: 'https://262.ecma-international.org/5.1/#sec-9.1',
	ToPropertyDescriptor: 'https://262.ecma-international.org/5.1/#sec-8.10.5',
	ToString: 'https://262.ecma-international.org/5.1/#sec-9.8',
	ToUint16: 'https://262.ecma-international.org/5.1/#sec-9.7',
	ToUint32: 'https://262.ecma-international.org/5.1/#sec-9.6',
	Type: 'https://262.ecma-international.org/5.1/#sec-8',
	WeekDay: 'https://262.ecma-international.org/5.1/#sec-********',
	YearFromTime: 'https://262.ecma-international.org/5.1/#sec-********'
};
