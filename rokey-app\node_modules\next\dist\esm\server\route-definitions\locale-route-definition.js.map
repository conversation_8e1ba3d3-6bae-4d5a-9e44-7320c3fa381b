{"version": 3, "sources": ["../../../src/server/route-definitions/locale-route-definition.ts"], "sourcesContent": ["import type { RouteDefinition } from './route-definition'\nimport type { RouteKind } from '../route-kind'\n\nexport interface LocaleRouteDefinition<K extends RouteKind = RouteKind>\n  extends RouteDefinition<K> {\n  /**\n   * When defined it means that this route is locale aware. When undefined,\n   * it means no special handling has to occur to process locales.\n   */\n  i18n?: {\n    /**\n     * Describes the locale for the route. If this is undefined, then it\n     * indicates that this route can handle _any_ locale.\n     */\n    locale?: string\n  }\n}\n"], "names": [], "mappings": "AAGA,WAaC"}