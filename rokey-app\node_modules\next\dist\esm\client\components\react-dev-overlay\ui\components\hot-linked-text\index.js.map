{"version": 3, "sources": ["../../../../../../../src/client/components/react-dev-overlay/ui/components/hot-linked-text/index.tsx"], "sourcesContent": ["import React from 'react'\nimport {\n  decodeMagicIdentifier,\n  MAGIC_IDENTIFIER_REGEX,\n} from '../../../../../../shared/lib/magic-identifier'\n\nconst linkRegex = /https?:\\/\\/[^\\s/$.?#].[^\\s)'\"]*/i\n\nconst splitRegexp = new RegExp(`(${MAGIC_IDENTIFIER_REGEX.source}|\\\\s+)`)\n\nexport const HotlinkedText: React.FC<{\n  text: string\n  matcher?: (text: string) => boolean\n}> = function HotlinkedText(props) {\n  const { text, matcher } = props\n\n  const wordsAndWhitespaces = text.split(splitRegexp)\n\n  return (\n    <>\n      {wordsAndWhitespaces.map((word, index) => {\n        if (linkRegex.test(word)) {\n          const link = linkRegex.exec(word)!\n          const href = link[0]\n          // If link matcher is present but the link doesn't match, don't turn it into a link\n          if (typeof matcher === 'function' && !matcher(href)) {\n            return word\n          }\n          return (\n            <React.Fragment key={`link-${index}`}>\n              <a href={href} target=\"_blank\" rel=\"noreferrer noopener\">\n                {word}\n              </a>\n            </React.Fragment>\n          )\n        }\n        try {\n          const decodedWord = decodeMagicIdentifier(word)\n          if (decodedWord !== word) {\n            return (\n              <i key={`ident-${index}`}>\n                {'{'}\n                {decodedWord}\n                {'}'}\n              </i>\n            )\n          }\n        } catch (e) {\n          return (\n            <i key={`ident-${index}`}>\n              {'{'}\n              {word} (decoding failed: {'' + e}){'}'}\n            </i>\n          )\n        }\n        return <React.Fragment key={`text-${index}`}>{word}</React.Fragment>\n      })}\n    </>\n  )\n}\n"], "names": ["React", "decodeMagicIdentifier", "MAGIC_IDENTIFIER_REGEX", "linkRegex", "splitRegexp", "RegExp", "source", "HotlinkedText", "props", "text", "matcher", "wordsAndWhitespaces", "split", "map", "word", "index", "test", "link", "exec", "href", "Fragment", "a", "target", "rel", "decodedWord", "i", "e"], "mappings": ";AAAA,OAAOA,WAAW,QAAO;AACzB,SACEC,qBAAqB,EACrBC,sBAAsB,QACjB,gDAA+C;AAEtD,MAAMC,YAAY;AAElB,MAAMC,cAAc,IAAIC,OAAO,AAAC,MAAGH,uBAAuBI,MAAM,GAAC;AAEjE,OAAO,MAAMC,gBAGR,SAASA,cAAcC,KAAK;IAC/B,MAAM,EAAEC,IAAI,EAAEC,OAAO,EAAE,GAAGF;IAE1B,MAAMG,sBAAsBF,KAAKG,KAAK,CAACR;IAEvC,qBACE;kBACGO,oBAAoBE,GAAG,CAAC,CAACC,MAAMC;YAC9B,IAAIZ,UAAUa,IAAI,CAACF,OAAO;gBACxB,MAAMG,OAAOd,UAAUe,IAAI,CAACJ;gBAC5B,MAAMK,OAAOF,IAAI,CAAC,EAAE;gBACpB,mFAAmF;gBACnF,IAAI,OAAOP,YAAY,cAAc,CAACA,QAAQS,OAAO;oBACnD,OAAOL;gBACT;gBACA,qBACE,KAACd,MAAMoB,QAAQ;8BACb,cAAA,KAACC;wBAAEF,MAAMA;wBAAMG,QAAO;wBAASC,KAAI;kCAChCT;;mBAFgB,AAAC,UAAOC;YAMjC;YACA,IAAI;gBACF,MAAMS,cAAcvB,sBAAsBa;gBAC1C,IAAIU,gBAAgBV,MAAM;oBACxB,qBACE,MAACW;;4BACE;4BACAD;4BACA;;uBAHK,AAAC,WAAQT;gBAMrB;YACF,EAAE,OAAOW,GAAG;gBACV,qBACE,MAACD;;wBACE;wBACAX;wBAAK;wBAAoB,KAAKY;wBAAE;wBAAE;;mBAF7B,AAAC,WAAQX;YAKrB;YACA,qBAAO,KAACf,MAAMoB,QAAQ;0BAAwBN;eAAlB,AAAC,UAAOC;QACtC;;AAGN,EAAC"}