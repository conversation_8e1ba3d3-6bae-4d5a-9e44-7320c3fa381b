import { BinaryOperatorAggregate } from "../channels/binop.js";
import { LastValue } from "../channels/last_value.js";
import { isConfiguredManagedValue, } from "../managed/base.js";
/**
 * Should not be instantiated directly. See {@link Annotation}.
 */
export class AnnotationRoot {
    constructor(s) {
        Object.defineProperty(this, "lc_graph_name", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: "AnnotationRoot"
        });
        Object.defineProperty(this, "spec", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.spec = s;
    }
}
/**
 * Helper that instantiates channels within a StateGraph state.
 *
 * Can be used as a field in an {@link Annotation.Root} wrapper in one of two ways:
 * 1. **Directly**: Creates a channel that stores the most recent value returned from a node.
 * 2. **With a reducer**: Creates a channel that applies the reducer on a node's return value.
 *
 * @example
 * ```ts
 * import { StateGraph, Annotation } from "@langchain/langgraph";
 *
 * // Define a state with a single string key named "currentOutput"
 * const SimpleAnnotation = Annotation.Root({
 *   currentOutput: Annotation<string>,
 * });
 *
 * const graphBuilder = new StateGraph(SimpleAnnotation);
 *
 * // A node in the graph that returns an object with a "currentOutput" key
 * // replaces the value in the state. You can get the state type as shown below:
 * const myNode = (state: typeof SimpleAnnotation.State) => {
 *   return {
 *     currentOutput: "some_new_value",
 *   };
 * }
 *
 * const graph = graphBuilder
 *   .addNode("myNode", myNode)
 *   ...
 *   .compile();
 * ```
 *
 * @example
 * ```ts
 * import { type BaseMessage, AIMessage } from "@langchain/core/messages";
 * import { StateGraph, Annotation } from "@langchain/langgraph";
 *
 * // Define a state with a single key named "messages" that will
 * // combine a returned BaseMessage or arrays of BaseMessages
 * const AnnotationWithReducer = Annotation.Root({
 *   messages: Annotation<BaseMessage[]>({
 *     // Different types are allowed for updates
 *     reducer: (left: BaseMessage[], right: BaseMessage | BaseMessage[]) => {
 *       if (Array.isArray(right)) {
 *         return left.concat(right);
 *       }
 *       return left.concat([right]);
 *     },
 *     default: () => [],
 *   }),
 * });
 *
 * const graphBuilder = new StateGraph(AnnotationWithReducer);
 *
 * // A node in the graph that returns an object with a "messages" key
 * // will update the state by combining the existing value with the returned one.
 * const myNode = (state: typeof AnnotationWithReducer.State) => {
 *   return {
 *     messages: [new AIMessage("Some new response")],
 *   };
 * };
 *
 * const graph = graphBuilder
 *   .addNode("myNode", myNode)
 *   ...
 *   .compile();
 * ```
 * @namespace
 * @property Root
 * Helper function that instantiates a StateGraph state. See {@link Annotation} for usage.
 */
export const Annotation = function (annotation) {
    if (isConfiguredManagedValue(annotation)) {
        return annotation;
    }
    else if (annotation) {
        return getChannel(annotation);
    }
    else {
        // @ts-expect-error - Annotation without reducer
        return new LastValue();
    }
};
Annotation.Root = (sd) => new AnnotationRoot(sd);
export function getChannel(reducer) {
    if (typeof reducer === "object" &&
        reducer &&
        "reducer" in reducer &&
        reducer.reducer) {
        return new BinaryOperatorAggregate(reducer.reducer, reducer.default);
    }
    if (typeof reducer === "object" &&
        reducer &&
        "value" in reducer &&
        reducer.value) {
        return new BinaryOperatorAggregate(reducer.value, reducer.default);
    }
    // @ts-expect-error - Annotation without reducer
    return new LastValue();
}
//# sourceMappingURL=annotation.js.map