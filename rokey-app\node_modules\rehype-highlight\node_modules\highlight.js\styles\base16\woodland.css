pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
/*!
  Theme: Woodland
  Author: <PERSON> (https://jcornwall.com)
  License: ~ MIT (or more permissive) [via base16-schemes-source]
  Maintainer: @highlightjs/core-team
  Version: 2021.09.0
*/
/*
  WARNING: DO NOT EDIT THIS FILE DIRECTLY.

  This theme file was auto-generated from the Base16 scheme woodland
  by the Highlight.js Base16 template builder.

  - https://github.com/highlightjs/base16-highlightjs
*/
/*
base00  #231e18  Default Background
base01  #302b25  Lighter Background (Used for status bars, line number and folding marks)
base02  #48413a  Selection Background
base03  #9d8b70  Comments, Invisibles, Line Highlighting
base04  #b4a490  Dark Foreground (Used for status bars)
base05  #cabcb1  Default Foreground, Caret, Delimiters, Operators
base06  #d7c8bc  Light Foreground (Not often used)
base07  #e4d4c8  Light Background (Not often used)
base08  #d35c5c  Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted
base09  #ca7f32  Integers, Boolean, Constants, XML Attributes, Markup Link Url
base0A  #e0ac16  Classes, Markup Bold, Search Text Background
base0B  #b7ba53  Strings, Inherited Class, Markup Code, Diff Inserted
base0C  #6eb958  Support, Regular Expressions, Escape Characters, Markup Quotes
base0D  #88a4d3  Functions, Methods, Attribute IDs, Headings
base0E  #bb90e2  Keywords, Storage, Selector, Markup Italic, Diff Changed
base0F  #b49368  Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?>
*/
pre code.hljs {
  display: block;
  overflow-x: auto;
  padding: 1em
}
code.hljs {
  padding: 3px 5px
}
.hljs {
  color: #cabcb1;
  background: #231e18
}
.hljs::selection,
.hljs ::selection {
  background-color: #48413a;
  color: #cabcb1
}
/* purposely do not highlight these things */
.hljs-formula,
.hljs-params,
.hljs-property {
  
}
/* base03 - #9d8b70 -  Comments, Invisibles, Line Highlighting */
.hljs-comment {
  color: #9d8b70
}
/* base04 - #b4a490 -  Dark Foreground (Used for status bars) */
.hljs-tag {
  color: #b4a490
}
/* base05 - #cabcb1 -  Default Foreground, Caret, Delimiters, Operators */
.hljs-subst,
.hljs-punctuation,
.hljs-operator {
  color: #cabcb1
}
.hljs-operator {
  opacity: 0.7
}
/* base08 - Variables, XML Tags, Markup Link Text, Markup Lists, Diff Deleted */
.hljs-bullet,
.hljs-variable,
.hljs-template-variable,
.hljs-selector-tag,
.hljs-name,
.hljs-deletion {
  color: #d35c5c
}
/* base09 - Integers, Boolean, Constants, XML Attributes, Markup Link Url */
.hljs-symbol,
.hljs-number,
.hljs-link,
.hljs-attr,
.hljs-variable.constant_,
.hljs-literal {
  color: #ca7f32
}
/* base0A - Classes, Markup Bold, Search Text Background */
.hljs-title,
.hljs-class .hljs-title,
.hljs-title.class_ {
  color: #e0ac16
}
.hljs-strong {
  font-weight: bold;
  color: #e0ac16
}
/* base0B - Strings, Inherited Class, Markup Code, Diff Inserted */
.hljs-code,
.hljs-addition,
.hljs-title.class_.inherited__,
.hljs-string {
  color: #b7ba53
}
/* base0C - Support, Regular Expressions, Escape Characters, Markup Quotes */
/* guessing */
.hljs-built_in,
.hljs-doctag,
.hljs-quote,
.hljs-keyword.hljs-atrule,
.hljs-regexp {
  color: #6eb958
}
/* base0D - Functions, Methods, Attribute IDs, Headings */
.hljs-function .hljs-title,
.hljs-attribute,
.ruby .hljs-property,
.hljs-title.function_,
.hljs-section {
  color: #88a4d3
}
/* base0E - Keywords, Storage, Selector, Markup Italic, Diff Changed */
/* .hljs-selector-id, */
/* .hljs-selector-class, */
/* .hljs-selector-attr, */
/* .hljs-selector-pseudo, */
.hljs-type,
.hljs-template-tag,
.diff .hljs-meta,
.hljs-keyword {
  color: #bb90e2
}
.hljs-emphasis {
  color: #bb90e2;
  font-style: italic
}
/* base0F - Deprecated, Opening/Closing Embedded Language Tags, e.g. <?php ?> */
/*
  prevent top level .keyword and .string scopes
  from leaking into meta by accident
*/
.hljs-meta,
.hljs-meta .hljs-keyword,
.hljs-meta .hljs-string {
  color: #b49368
}
/* for v10 compatible themes */
.hljs-meta .hljs-keyword,
.hljs-meta-keyword {
  font-weight: bold
}