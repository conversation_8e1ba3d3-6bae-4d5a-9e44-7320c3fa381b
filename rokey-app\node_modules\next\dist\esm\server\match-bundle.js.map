{"version": 3, "sources": ["../../src/server/match-bundle.ts"], "sourcesContent": ["import getRouteFromAssetPath from '../shared/lib/router/utils/get-route-from-asset-path'\n\nexport default function matchBundle(\n  regex: RegExp,\n  input: string\n): string | null {\n  const result = regex.exec(input)\n\n  if (!result) {\n    return null\n  }\n\n  return getRouteFromAssetPath(`/${result[1]}`)\n}\n"], "names": ["getRouteFromAssetPath", "matchBundle", "regex", "input", "result", "exec"], "mappings": "AAAA,OAAOA,2BAA2B,uDAAsD;AAExF,eAAe,SAASC,YACtBC,KAAa,EACbC,KAAa;IAEb,MAAMC,SAASF,MAAMG,IAAI,CAACF;IAE1B,IAAI,CAACC,QAAQ;QACX,OAAO;IACT;IAEA,OAAOJ,sBAAsB,CAAC,CAAC,EAAEI,MAAM,CAAC,EAAE,EAAE;AAC9C"}