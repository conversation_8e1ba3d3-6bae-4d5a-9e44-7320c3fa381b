// TODO: Merge with base LangChain error class when we drop support for core@0.2.0
export class BaseLangGraphError extends Error {
    constructor(message, fields) {
        let finalMessage = message ?? "";
        if (fields?.lc_error_code) {
            finalMessage = `${finalMessage}\n\nTroubleshooting URL: https://langchain-ai.github.io/langgraphjs/troubleshooting/errors/${fields.lc_error_code}/\n`;
        }
        super(finalMessage);
        Object.defineProperty(this, "lc_error_code", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.lc_error_code = fields?.lc_error_code;
    }
}
export class GraphBubbleUp extends BaseLangGraphError {
    get is_bubble_up() {
        return true;
    }
}
export class GraphRecursionError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "GraphRecursionError";
    }
    static get unminifiable_name() {
        return "GraphRecursionError";
    }
}
export class GraphValueError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "GraphValueError";
    }
    static get unminifiable_name() {
        return "GraphValueError";
    }
}
export class GraphInterrupt extends GraphBubbleUp {
    constructor(interrupts, fields) {
        super(JSON.stringify(interrupts, null, 2), fields);
        Object.defineProperty(this, "interrupts", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.name = "GraphInterrupt";
        this.interrupts = interrupts ?? [];
    }
    static get unminifiable_name() {
        return "GraphInterrupt";
    }
}
/** Raised by a node to interrupt execution. */
export class NodeInterrupt extends GraphInterrupt {
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    constructor(message, fields) {
        super([
            {
                value: message,
                when: "during",
            },
        ], fields);
        this.name = "NodeInterrupt";
    }
    static get unminifiable_name() {
        return "NodeInterrupt";
    }
}
export class ParentCommand extends GraphBubbleUp {
    constructor(command) {
        super();
        Object.defineProperty(this, "command", {
            enumerable: true,
            configurable: true,
            writable: true,
            value: void 0
        });
        this.name = "ParentCommand";
        this.command = command;
    }
    static get unminifiable_name() {
        return "ParentCommand";
    }
}
export function isParentCommand(e) {
    return (e !== undefined &&
        e.name === ParentCommand.unminifiable_name);
}
export function isGraphBubbleUp(e) {
    return e !== undefined && e.is_bubble_up === true;
}
export function isGraphInterrupt(e) {
    return (e !== undefined &&
        [
            GraphInterrupt.unminifiable_name,
            NodeInterrupt.unminifiable_name,
        ].includes(e.name));
}
export class EmptyInputError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "EmptyInputError";
    }
    static get unminifiable_name() {
        return "EmptyInputError";
    }
}
export class EmptyChannelError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "EmptyChannelError";
    }
    static get unminifiable_name() {
        return "EmptyChannelError";
    }
}
export class InvalidUpdateError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "InvalidUpdateError";
    }
    static get unminifiable_name() {
        return "InvalidUpdateError";
    }
}
/**
 * @deprecated This exception type is no longer thrown.
 */
export class MultipleSubgraphsError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "MultipleSubgraphError";
    }
    static get unminifiable_name() {
        return "MultipleSubgraphError";
    }
}
export class UnreachableNodeError extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "UnreachableNodeError";
    }
    static get unminifiable_name() {
        return "UnreachableNodeError";
    }
}
/**
 * Exception raised when an error occurs in the remote graph.
 */
export class RemoteException extends BaseLangGraphError {
    constructor(message, fields) {
        super(message, fields);
        this.name = "RemoteException";
    }
    static get unminifiable_name() {
        return "RemoteException";
    }
}
/**
 * Used for subgraph detection.
 */
export const getSubgraphsSeenSet = () => {
    if (
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    globalThis[Symbol.for("LG_CHECKPOINT_SEEN_NS_SET")] === undefined) {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        globalThis[Symbol.for("LG_CHECKPOINT_SEEN_NS_SET")] = new Set();
    }
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    return globalThis[Symbol.for("LG_CHECKPOINT_SEEN_NS_SET")];
};
//# sourceMappingURL=errors.js.map