{"version": 3, "sources": ["../../../src/server/app-render/make-get-server-inserted-html.tsx"], "sourcesContent": ["import React, { type JSX } from 'react'\nimport { isHTTPAccessFallbackError } from '../../client/components/http-access-fallback/http-access-fallback'\nimport {\n  getURLFromRedirectError,\n  getRedirectStatusCodeFromError,\n} from '../../client/components/redirect'\nimport { isRedirectError } from '../../client/components/redirect-error'\nimport { renderToReadableStream } from 'react-dom/server.edge'\nimport { streamToString } from '../stream-utils/node-web-streams-helper'\nimport { RedirectStatusCode } from '../../client/components/redirect-status-code'\nimport { addPathPrefix } from '../../shared/lib/router/utils/add-path-prefix'\nimport type { ClientTraceDataEntry } from '../lib/trace/tracer'\n\nexport function makeGetServerInsertedHTML({\n  polyfills,\n  renderServerInsertedHTML,\n  serverCapturedErrors,\n  tracingMetadata,\n  basePath,\n}: {\n  polyfills: JSX.IntrinsicElements['script'][]\n  renderServerInsertedHTML: () => React.ReactNode\n  tracingMetadata: ClientTraceDataEntry[] | undefined\n  serverCapturedErrors: Array<unknown>\n  basePath: string\n}) {\n  let flushedErrorMetaTagsUntilIndex = 0\n  // flag for static content that only needs to be flushed once\n  let hasFlushedInitially = false\n\n  const polyfillTags = polyfills.map((polyfill) => {\n    return <script key={polyfill.src} {...polyfill} />\n  })\n\n  return async function getServerInsertedHTML() {\n    // Loop through all the errors that have been captured but not yet\n    // flushed.\n    const errorMetaTags = []\n    while (flushedErrorMetaTagsUntilIndex < serverCapturedErrors.length) {\n      const error = serverCapturedErrors[flushedErrorMetaTagsUntilIndex]\n      flushedErrorMetaTagsUntilIndex++\n\n      if (isHTTPAccessFallbackError(error)) {\n        errorMetaTags.push(\n          <meta name=\"robots\" content=\"noindex\" key={error.digest} />,\n          process.env.NODE_ENV === 'development' ? (\n            <meta name=\"next-error\" content=\"not-found\" key=\"next-error\" />\n          ) : null\n        )\n      } else if (isRedirectError(error)) {\n        const redirectUrl = addPathPrefix(\n          getURLFromRedirectError(error),\n          basePath\n        )\n        const statusCode = getRedirectStatusCodeFromError(error)\n        const isPermanent =\n          statusCode === RedirectStatusCode.PermanentRedirect ? true : false\n        if (redirectUrl) {\n          errorMetaTags.push(\n            <meta\n              id=\"__next-page-redirect\"\n              httpEquiv=\"refresh\"\n              content={`${isPermanent ? 0 : 1};url=${redirectUrl}`}\n              key={error.digest}\n            />\n          )\n        }\n      }\n    }\n\n    const traceMetaTags = (tracingMetadata || []).map(\n      ({ key, value }, index) => (\n        <meta key={`next-trace-data-${index}`} name={key} content={value} />\n      )\n    )\n\n    const serverInsertedHTML = renderServerInsertedHTML()\n\n    // Skip React rendering if we know the content is empty.\n    if (\n      polyfillTags.length === 0 &&\n      traceMetaTags.length === 0 &&\n      errorMetaTags.length === 0 &&\n      Array.isArray(serverInsertedHTML) &&\n      serverInsertedHTML.length === 0\n    ) {\n      return ''\n    }\n\n    const stream = await renderToReadableStream(\n      <>\n        {\n          /* Insert the polyfills if they haven't been flushed yet. */\n          hasFlushedInitially ? null : polyfillTags\n        }\n        {serverInsertedHTML}\n        {hasFlushedInitially ? null : traceMetaTags}\n        {errorMetaTags}\n      </>,\n      {\n        // Larger chunk because this isn't sent over the network.\n        // Let's set it to 1MB.\n        progressiveChunkSize: 1024 * 1024,\n      }\n    )\n\n    hasFlushedInitially = true\n\n    // There's no need to wait for the stream to be ready\n    // e.g. calling `await stream.allReady` because `streamToString` will\n    // wait and decode the stream progressively with better parallelism.\n    return streamToString(stream)\n  }\n}\n"], "names": ["React", "isHTTPAccessFallbackError", "getURLFromRedirectError", "getRedirectStatusCodeFromError", "isRedirectError", "renderToReadableStream", "streamToString", "RedirectStatusCode", "addPathPrefix", "makeGetServerInsertedHTML", "polyfills", "renderServerInsertedHTML", "serverCapturedErrors", "tracingMetadata", "basePath", "flushedErrorMetaTagsUntilIndex", "hasFlushedInitially", "polyfillTags", "map", "polyfill", "script", "src", "getServerInsertedHTML", "errorMetaTags", "length", "error", "push", "meta", "name", "content", "digest", "process", "env", "NODE_ENV", "redirectUrl", "statusCode", "isPermanent", "PermanentRedirect", "id", "httpEquiv", "traceMetaTags", "key", "value", "index", "serverInsertedHTML", "Array", "isArray", "stream", "progressiveChunkSize"], "mappings": ";AAAA,OAAOA,WAAyB,QAAO;AACvC,SAASC,yBAAyB,QAAQ,oEAAmE;AAC7G,SACEC,uBAAuB,EACvBC,8BAA8B,QACzB,mCAAkC;AACzC,SAASC,eAAe,QAAQ,yCAAwC;AACxE,SAASC,sBAAsB,QAAQ,wBAAuB;AAC9D,SAASC,cAAc,QAAQ,0CAAyC;AACxE,SAASC,kBAAkB,QAAQ,+CAA8C;AACjF,SAASC,aAAa,QAAQ,gDAA+C;AAG7E,OAAO,SAASC,0BAA0B,EACxCC,SAAS,EACTC,wBAAwB,EACxBC,oBAAoB,EACpBC,eAAe,EACfC,QAAQ,EAOT;IACC,IAAIC,iCAAiC;IACrC,6DAA6D;IAC7D,IAAIC,sBAAsB;IAE1B,MAAMC,eAAeP,UAAUQ,GAAG,CAAC,CAACC;QAClC,qBAAO,KAACC;YAA2B,GAAGD,QAAQ;WAA1BA,SAASE,GAAG;IAClC;IAEA,OAAO,eAAeC;QACpB,kEAAkE;QAClE,WAAW;QACX,MAAMC,gBAAgB,EAAE;QACxB,MAAOR,iCAAiCH,qBAAqBY,MAAM,CAAE;YACnE,MAAMC,QAAQb,oBAAoB,CAACG,+BAA+B;YAClEA;YAEA,IAAId,0BAA0BwB,QAAQ;gBACpCF,cAAcG,IAAI,eAChB,KAACC;oBAAKC,MAAK;oBAASC,SAAQ;mBAAeJ,MAAMK,MAAM,GACvDC,QAAQC,GAAG,CAACC,QAAQ,KAAK,8BACvB,KAACN;oBAAKC,MAAK;oBAAaC,SAAQ;mBAAgB,gBAC9C;YAER,OAAO,IAAIzB,gBAAgBqB,QAAQ;gBACjC,MAAMS,cAAc1B,cAClBN,wBAAwBuB,QACxBX;gBAEF,MAAMqB,aAAahC,+BAA+BsB;gBAClD,MAAMW,cACJD,eAAe5B,mBAAmB8B,iBAAiB,GAAG,OAAO;gBAC/D,IAAIH,aAAa;oBACfX,cAAcG,IAAI,eAChB,KAACC;wBACCW,IAAG;wBACHC,WAAU;wBACVV,SAAS,GAAGO,cAAc,IAAI,EAAE,KAAK,EAAEF,aAAa;uBAC/CT,MAAMK,MAAM;gBAGvB;YACF;QACF;QAEA,MAAMU,gBAAgB,AAAC3B,CAAAA,mBAAmB,EAAE,AAAD,EAAGK,GAAG,CAC/C,CAAC,EAAEuB,GAAG,EAAEC,KAAK,EAAE,EAAEC,sBACf,KAAChB;gBAAsCC,MAAMa;gBAAKZ,SAASa;eAAhD,CAAC,gBAAgB,EAAEC,OAAO;QAIzC,MAAMC,qBAAqBjC;QAE3B,wDAAwD;QACxD,IACEM,aAAaO,MAAM,KAAK,KACxBgB,cAAchB,MAAM,KAAK,KACzBD,cAAcC,MAAM,KAAK,KACzBqB,MAAMC,OAAO,CAACF,uBACdA,mBAAmBpB,MAAM,KAAK,GAC9B;YACA,OAAO;QACT;QAEA,MAAMuB,SAAS,MAAM1C,qCACnB;;gBAEI,0DAA0D,GAC1DW,sBAAsB,OAAOC;gBAE9B2B;gBACA5B,sBAAsB,OAAOwB;gBAC7BjB;;YAEH;YACE,yDAAyD;YACzD,uBAAuB;YACvByB,sBAAsB,OAAO;QAC/B;QAGFhC,sBAAsB;QAEtB,qDAAqD;QACrD,qEAAqE;QACrE,oEAAoE;QACpE,OAAOV,eAAeyC;IACxB;AACF"}