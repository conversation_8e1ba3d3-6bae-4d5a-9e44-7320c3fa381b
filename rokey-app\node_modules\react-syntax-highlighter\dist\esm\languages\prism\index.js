export { default as abap } from './abap';
export { default as abnf } from './abnf';
export { default as actionscript } from './actionscript';
export { default as ada } from './ada';
export { default as agda } from './agda';
export { default as al } from './al';
export { default as antlr4 } from './antlr4';
export { default as apacheconf } from './apacheconf';
export { default as apex } from './apex';
export { default as apl } from './apl';
export { default as applescript } from './applescript';
export { default as aql } from './aql';
export { default as arduino } from './arduino';
export { default as arff } from './arff';
export { default as asciidoc } from './asciidoc';
export { default as asm6502 } from './asm6502';
export { default as asmatmel } from './asmatmel';
export { default as aspnet } from './aspnet';
export { default as autohotkey } from './autohotkey';
export { default as autoit } from './autoit';
export { default as avisynth } from './avisynth';
export { default as avroIdl } from './avro-idl';
export { default as bash } from './bash';
export { default as basic } from './basic';
export { default as batch } from './batch';
export { default as bbcode } from './bbcode';
export { default as bicep } from './bicep';
export { default as birb } from './birb';
export { default as bison } from './bison';
export { default as bnf } from './bnf';
export { default as brainfuck } from './brainfuck';
export { default as brightscript } from './brightscript';
export { default as bro } from './bro';
export { default as bsl } from './bsl';
export { default as c } from './c';
export { default as cfscript } from './cfscript';
export { default as chaiscript } from './chaiscript';
export { default as cil } from './cil';
export { default as clike } from './clike';
export { default as clojure } from './clojure';
export { default as cmake } from './cmake';
export { default as cobol } from './cobol';
export { default as coffeescript } from './coffeescript';
export { default as concurnas } from './concurnas';
export { default as coq } from './coq';
export { default as cpp } from './cpp';
export { default as crystal } from './crystal';
export { default as csharp } from './csharp';
export { default as cshtml } from './cshtml';
export { default as csp } from './csp';
export { default as cssExtras } from './css-extras';
export { default as css } from './css';
export { default as csv } from './csv';
export { default as cypher } from './cypher';
export { default as d } from './d';
export { default as dart } from './dart';
export { default as dataweave } from './dataweave';
export { default as dax } from './dax';
export { default as dhall } from './dhall';
export { default as diff } from './diff';
export { default as django } from './django';
export { default as dnsZoneFile } from './dns-zone-file';
export { default as docker } from './docker';
export { default as dot } from './dot';
export { default as ebnf } from './ebnf';
export { default as editorconfig } from './editorconfig';
export { default as eiffel } from './eiffel';
export { default as ejs } from './ejs';
export { default as elixir } from './elixir';
export { default as elm } from './elm';
export { default as erb } from './erb';
export { default as erlang } from './erlang';
export { default as etlua } from './etlua';
export { default as excelFormula } from './excel-formula';
export { default as factor } from './factor';
export { default as falselang } from './false';
export { default as firestoreSecurityRules } from './firestore-security-rules';
export { default as flow } from './flow';
export { default as fortran } from './fortran';
export { default as fsharp } from './fsharp';
export { default as ftl } from './ftl';
export { default as gap } from './gap';
export { default as gcode } from './gcode';
export { default as gdscript } from './gdscript';
export { default as gedcom } from './gedcom';
export { default as gherkin } from './gherkin';
export { default as git } from './git';
export { default as glsl } from './glsl';
export { default as gml } from './gml';
export { default as gn } from './gn';
export { default as goModule } from './go-module';
export { default as go } from './go';
export { default as graphql } from './graphql';
export { default as groovy } from './groovy';
export { default as haml } from './haml';
export { default as handlebars } from './handlebars';
export { default as haskell } from './haskell';
export { default as haxe } from './haxe';
export { default as hcl } from './hcl';
export { default as hlsl } from './hlsl';
export { default as hoon } from './hoon';
export { default as hpkp } from './hpkp';
export { default as hsts } from './hsts';
export { default as http } from './http';
export { default as ichigojam } from './ichigojam';
export { default as icon } from './icon';
export { default as icuMessageFormat } from './icu-message-format';
export { default as idris } from './idris';
export { default as iecst } from './iecst';
export { default as ignore } from './ignore';
export { default as inform7 } from './inform7';
export { default as ini } from './ini';
export { default as io } from './io';
export { default as j } from './j';
export { default as java } from './java';
export { default as javadoc } from './javadoc';
export { default as javadoclike } from './javadoclike';
export { default as javascript } from './javascript';
export { default as javastacktrace } from './javastacktrace';
export { default as jexl } from './jexl';
export { default as jolie } from './jolie';
export { default as jq } from './jq';
export { default as jsExtras } from './js-extras';
export { default as jsTemplates } from './js-templates';
export { default as jsdoc } from './jsdoc';
export { default as json } from './json';
export { default as json5 } from './json5';
export { default as jsonp } from './jsonp';
export { default as jsstacktrace } from './jsstacktrace';
export { default as jsx } from './jsx';
export { default as julia } from './julia';
export { default as keepalived } from './keepalived';
export { default as keyman } from './keyman';
export { default as kotlin } from './kotlin';
export { default as kumir } from './kumir';
export { default as kusto } from './kusto';
export { default as latex } from './latex';
export { default as latte } from './latte';
export { default as less } from './less';
export { default as lilypond } from './lilypond';
export { default as liquid } from './liquid';
export { default as lisp } from './lisp';
export { default as livescript } from './livescript';
export { default as llvm } from './llvm';
export { default as log } from './log';
export { default as lolcode } from './lolcode';
export { default as lua } from './lua';
export { default as magma } from './magma';
export { default as makefile } from './makefile';
export { default as markdown } from './markdown';
export { default as markupTemplating } from './markup-templating';
export { default as markup } from './markup';
export { default as matlab } from './matlab';
export { default as maxscript } from './maxscript';
export { default as mel } from './mel';
export { default as mermaid } from './mermaid';
export { default as mizar } from './mizar';
export { default as mongodb } from './mongodb';
export { default as monkey } from './monkey';
export { default as moonscript } from './moonscript';
export { default as n1ql } from './n1ql';
export { default as n4js } from './n4js';
export { default as nand2tetrisHdl } from './nand2tetris-hdl';
export { default as naniscript } from './naniscript';
export { default as nasm } from './nasm';
export { default as neon } from './neon';
export { default as nevod } from './nevod';
export { default as nginx } from './nginx';
export { default as nim } from './nim';
export { default as nix } from './nix';
export { default as nsis } from './nsis';
export { default as objectivec } from './objectivec';
export { default as ocaml } from './ocaml';
export { default as opencl } from './opencl';
export { default as openqasm } from './openqasm';
export { default as oz } from './oz';
export { default as parigp } from './parigp';
export { default as parser } from './parser';
export { default as pascal } from './pascal';
export { default as pascaligo } from './pascaligo';
export { default as pcaxis } from './pcaxis';
export { default as peoplecode } from './peoplecode';
export { default as perl } from './perl';
export { default as phpExtras } from './php-extras';
export { default as php } from './php';
export { default as phpdoc } from './phpdoc';
export { default as plsql } from './plsql';
export { default as powerquery } from './powerquery';
export { default as powershell } from './powershell';
export { default as processing } from './processing';
export { default as prolog } from './prolog';
export { default as promql } from './promql';
export { default as properties } from './properties';
export { default as protobuf } from './protobuf';
export { default as psl } from './psl';
export { default as pug } from './pug';
export { default as puppet } from './puppet';
export { default as pure } from './pure';
export { default as purebasic } from './purebasic';
export { default as purescript } from './purescript';
export { default as python } from './python';
export { default as q } from './q';
export { default as qml } from './qml';
export { default as qore } from './qore';
export { default as qsharp } from './qsharp';
export { default as r } from './r';
export { default as racket } from './racket';
export { default as reason } from './reason';
export { default as regex } from './regex';
export { default as rego } from './rego';
export { default as renpy } from './renpy';
export { default as rest } from './rest';
export { default as rip } from './rip';
export { default as roboconf } from './roboconf';
export { default as robotframework } from './robotframework';
export { default as ruby } from './ruby';
export { default as rust } from './rust';
export { default as sas } from './sas';
export { default as sass } from './sass';
export { default as scala } from './scala';
export { default as scheme } from './scheme';
export { default as scss } from './scss';
export { default as shellSession } from './shell-session';
export { default as smali } from './smali';
export { default as smalltalk } from './smalltalk';
export { default as smarty } from './smarty';
export { default as sml } from './sml';
export { default as solidity } from './solidity';
export { default as solutionFile } from './solution-file';
export { default as soy } from './soy';
export { default as sparql } from './sparql';
export { default as splunkSpl } from './splunk-spl';
export { default as sqf } from './sqf';
export { default as sql } from './sql';
export { default as squirrel } from './squirrel';
export { default as stan } from './stan';
export { default as stylus } from './stylus';
export { default as swift } from './swift';
export { default as systemd } from './systemd';
export { default as t4Cs } from './t4-cs';
export { default as t4Templating } from './t4-templating';
export { default as t4Vb } from './t4-vb';
export { default as tap } from './tap';
export { default as tcl } from './tcl';
export { default as textile } from './textile';
export { default as toml } from './toml';
export { default as tremor } from './tremor';
export { default as tsx } from './tsx';
export { default as tt2 } from './tt2';
export { default as turtle } from './turtle';
export { default as twig } from './twig';
export { default as typescript } from './typescript';
export { default as typoscript } from './typoscript';
export { default as unrealscript } from './unrealscript';
export { default as uorazor } from './uorazor';
export { default as uri } from './uri';
export { default as v } from './v';
export { default as vala } from './vala';
export { default as vbnet } from './vbnet';
export { default as velocity } from './velocity';
export { default as verilog } from './verilog';
export { default as vhdl } from './vhdl';
export { default as vim } from './vim';
export { default as visualBasic } from './visual-basic';
export { default as warpscript } from './warpscript';
export { default as wasm } from './wasm';
export { default as webIdl } from './web-idl';
export { default as wiki } from './wiki';
export { default as wolfram } from './wolfram';
export { default as wren } from './wren';
export { default as xeora } from './xeora';
export { default as xmlDoc } from './xml-doc';
export { default as xojo } from './xojo';
export { default as xquery } from './xquery';
export { default as yaml } from './yaml';
export { default as yang } from './yang';
export { default as zig } from './zig';