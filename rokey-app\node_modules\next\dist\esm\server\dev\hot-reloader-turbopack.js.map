{"version": 3, "sources": ["../../../src/server/dev/hot-reloader-turbopack.ts"], "sourcesContent": ["import type { Socket } from 'net'\nimport { mkdir, writeFile } from 'fs/promises'\nimport { join, extname } from 'path'\nimport { pathToFileURL } from 'url'\n\nimport ws from 'next/dist/compiled/ws'\n\nimport type { OutputState } from '../../build/output/store'\nimport { store as consoleStore } from '../../build/output/store'\nimport type {\n  CompilationError,\n  HMR_ACTION_TYPES,\n  NextJsHotReloaderInterface,\n  ReloadPageAction,\n  SyncAction,\n  TurbopackConnectedAction,\n} from './hot-reloader-types'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from './hot-reloader-types'\nimport type {\n  Update as TurbopackUpdate,\n  Endpoint,\n  WrittenEndpoint,\n  TurbopackResult,\n  Project,\n  Entrypoints,\n} from '../../build/swc/types'\nimport { createDefineEnv } from '../../build/swc'\nimport * as Log from '../../build/output/log'\nimport {\n  getVersionInfo,\n  matchNextPageBundleRequest,\n} from './hot-reloader-webpack'\nimport { BLOCKED_PAGES } from '../../shared/lib/constants'\nimport {\n  getOverlayMiddleware,\n  getSourceMapMiddleware,\n} from '../../client/components/react-dev-overlay/server/middleware-turbopack'\nimport { PageNotFoundError } from '../../shared/lib/utils'\nimport { debounce } from '../utils'\nimport { deleteCache, deleteFromRequireCache } from './require-cache'\nimport {\n  clearAllModuleContexts,\n  clearModuleContext,\n} from '../lib/render-server'\nimport { denormalizePagePath } from '../../shared/lib/page-path/denormalize-page-path'\nimport { trace } from '../../trace'\nimport {\n  AssetMapper,\n  type ChangeSubscriptions,\n  type ClientState,\n  handleEntrypoints,\n  handlePagesErrorRoute,\n  handleRouteType,\n  hasEntrypointForKey,\n  msToNs,\n  type ReadyIds,\n  type SendHmr,\n  type StartBuilding,\n  processTopLevelIssues,\n  printNonFatalIssue,\n  normalizedPageToTurbopackStructureRoute,\n} from './turbopack-utils'\nimport {\n  propagateServerField,\n  type ServerFields,\n  type SetupOpts,\n} from '../lib/router-utils/setup-dev-bundler'\nimport { TurbopackManifestLoader } from '../../shared/lib/turbopack/manifest-loader'\nimport { findPagePathData } from './on-demand-entry-handler'\nimport type { RouteDefinition } from '../route-definitions/route-definition'\nimport {\n  type EntryKey,\n  getEntryKey,\n  splitEntryKey,\n} from '../../shared/lib/turbopack/entry-key'\nimport { FAST_REFRESH_RUNTIME_RELOAD } from './messages'\nimport { generateEncryptionKeyBase64 } from '../app-render/encryption-utils-server'\nimport { isAppPageRouteDefinition } from '../route-definitions/app-page-route-definition'\nimport { normalizeAppPath } from '../../shared/lib/router/utils/app-paths'\nimport { getNodeDebugType } from '../lib/utils'\nimport { isMetadataRouteFile } from '../../lib/metadata/is-metadata-route'\nimport {\n  setBundlerFindSourceMapImplementation,\n  type ModernSourceMapPayload,\n} from '../patch-error-inspect'\nimport { getNextErrorFeedbackMiddleware } from '../../client/components/react-dev-overlay/server/get-next-error-feedback-middleware'\nimport {\n  formatIssue,\n  getTurbopackJsConfig,\n  isPersistentCachingEnabled,\n  isWellKnownError,\n  processIssues,\n  renderStyledStringToErrorAnsi,\n  type EntryIssuesMap,\n  type TopLevelIssuesMap,\n} from '../../shared/lib/turbopack/utils'\nimport { getDevOverlayFontMiddleware } from '../../client/components/react-dev-overlay/font/get-dev-overlay-font-middleware'\nimport { devIndicatorServerState } from './dev-indicator-server-state'\nimport { getDisableDevIndicatorMiddleware } from './dev-indicator-middleware'\n// import { getSupportedBrowsers } from '../../build/utils'\n\nconst wsServer = new ws.Server({ noServer: true })\nconst isTestMode = !!(\n  process.env.NEXT_TEST_MODE ||\n  process.env.__NEXT_TEST_MODE ||\n  process.env.DEBUG\n)\n\nconst sessionId = Math.floor(Number.MAX_SAFE_INTEGER * Math.random())\n\n/**\n * Replaces turbopack:///[project] with the specified project in the `source` field.\n */\nfunction rewriteTurbopackSources(\n  projectRoot: string,\n  sourceMap: ModernSourceMapPayload\n): void {\n  if ('sections' in sourceMap) {\n    for (const section of sourceMap.sections) {\n      rewriteTurbopackSources(projectRoot, section.map)\n    }\n  } else {\n    for (let i = 0; i < sourceMap.sources.length; i++) {\n      sourceMap.sources[i] = pathToFileURL(\n        join(\n          projectRoot,\n          sourceMap.sources[i].replace(/turbopack:\\/\\/\\/\\[project\\]/, '')\n        )\n      ).toString()\n    }\n  }\n}\n\nfunction getSourceMapFromTurbopack(\n  project: Project,\n  projectRoot: string,\n  sourceURL: string\n): ModernSourceMapPayload | undefined {\n  let sourceMapJson: string | null = null\n\n  try {\n    sourceMapJson = project.getSourceMapSync(sourceURL)\n  } catch (err) {}\n\n  if (sourceMapJson === null) {\n    return undefined\n  } else {\n    const payload: ModernSourceMapPayload = JSON.parse(sourceMapJson)\n    // The sourcemap from Turbopack is not yet written to disk so its `sources`\n    // are not absolute paths yet. We need to rewrite them to be absolute paths.\n    rewriteTurbopackSources(projectRoot, payload)\n    return payload\n  }\n}\n\nexport async function createHotReloaderTurbopack(\n  opts: SetupOpts,\n  serverFields: ServerFields,\n  distDir: string,\n  resetFetch: () => void\n): Promise<NextJsHotReloaderInterface> {\n  const dev = true\n  const buildId = 'development'\n  const { nextConfig, dir: projectPath } = opts\n\n  const { loadBindings } =\n    require('../../build/swc') as typeof import('../../build/swc')\n\n  let bindings = await loadBindings()\n\n  // For the debugging purpose, check if createNext or equivalent next instance setup in test cases\n  // works correctly. Normally `run-test` hides output so only will be visible when `--debug` flag is used.\n  if (isTestMode) {\n    require('console').log('Creating turbopack project', {\n      dir: projectPath,\n      testMode: isTestMode,\n    })\n  }\n\n  const hasRewrites =\n    opts.fsChecker.rewrites.afterFiles.length > 0 ||\n    opts.fsChecker.rewrites.beforeFiles.length > 0 ||\n    opts.fsChecker.rewrites.fallback.length > 0\n\n  const hotReloaderSpan = trace('hot-reloader', undefined, {\n    version: process.env.__NEXT_VERSION as string,\n  })\n  // Ensure the hotReloaderSpan is flushed immediately as it's the parentSpan for all processing\n  // of the current `next dev` invocation.\n  hotReloaderSpan.stop()\n\n  const encryptionKey = await generateEncryptionKeyBase64({\n    isBuild: false,\n    distDir,\n  })\n\n  // TODO: Implement\n  let clientRouterFilters: any\n  if (nextConfig.experimental.clientRouterFilter) {\n    // TODO this need to be set correctly for persistent caching to work\n  }\n\n  // const supportedBrowsers = await getSupportedBrowsers(dir, dev)\n  const supportedBrowsers = [\n    'last 1 Chrome versions, last 1 Firefox versions, last 1 Safari versions, last 1 Edge versions',\n  ]\n\n  const project = await bindings.turbo.createProject(\n    {\n      projectPath: projectPath,\n      rootPath:\n        opts.nextConfig.turbopack?.root ||\n        opts.nextConfig.outputFileTracingRoot ||\n        projectPath,\n      distDir,\n      nextConfig: opts.nextConfig,\n      jsConfig: await getTurbopackJsConfig(projectPath, nextConfig),\n      watch: {\n        enable: dev,\n        pollIntervalMs: nextConfig.watchOptions?.pollIntervalMs,\n      },\n      dev,\n      env: process.env as Record<string, string>,\n      defineEnv: createDefineEnv({\n        isTurbopack: true,\n        clientRouterFilters,\n        config: nextConfig,\n        dev,\n        distDir,\n        fetchCacheKeyPrefix: opts.nextConfig.experimental.fetchCacheKeyPrefix,\n        hasRewrites,\n        // TODO: Implement\n        middlewareMatchers: undefined,\n      }),\n      buildId,\n      encryptionKey,\n      previewProps: opts.fsChecker.prerenderManifest.preview,\n      browserslistQuery: supportedBrowsers.join(', '),\n      noMangling: false,\n    },\n    {\n      persistentCaching: isPersistentCachingEnabled(opts.nextConfig),\n      memoryLimit: opts.nextConfig.experimental?.turbopackMemoryLimit,\n    }\n  )\n  setBundlerFindSourceMapImplementation(\n    getSourceMapFromTurbopack.bind(null, project, projectPath)\n  )\n  opts.onDevServerCleanup?.(async () => {\n    setBundlerFindSourceMapImplementation(() => undefined)\n    await project.onExit()\n  })\n  const entrypointsSubscription = project.entrypointsSubscribe()\n\n  const currentWrittenEntrypoints: Map<EntryKey, WrittenEndpoint> = new Map()\n  const currentEntrypoints: Entrypoints = {\n    global: {\n      app: undefined,\n      document: undefined,\n      error: undefined,\n\n      middleware: undefined,\n      instrumentation: undefined,\n    },\n\n    page: new Map(),\n    app: new Map(),\n  }\n\n  const currentTopLevelIssues: TopLevelIssuesMap = new Map()\n  const currentEntryIssues: EntryIssuesMap = new Map()\n\n  const manifestLoader = new TurbopackManifestLoader({\n    buildId,\n    distDir,\n    encryptionKey,\n  })\n\n  // Dev specific\n  const changeSubscriptions: ChangeSubscriptions = new Map()\n  const serverPathState = new Map<string, string>()\n  const readyIds: ReadyIds = new Set()\n  let currentEntriesHandlingResolve: ((value?: unknown) => void) | undefined\n  let currentEntriesHandling = new Promise(\n    (resolve) => (currentEntriesHandlingResolve = resolve)\n  )\n\n  const assetMapper = new AssetMapper()\n\n  function clearRequireCache(\n    key: EntryKey,\n    writtenEndpoint: WrittenEndpoint,\n    {\n      force,\n    }: {\n      // Always clear the cache, don't check if files have changed\n      force?: boolean\n    } = {}\n  ): boolean {\n    if (force) {\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        serverPathState.set(path, contentHash)\n      }\n    } else {\n      // Figure out if the server files have changed\n      let hasChange = false\n      for (const { path, contentHash } of writtenEndpoint.serverPaths) {\n        // We ignore source maps\n        if (path.endsWith('.map')) continue\n        const localKey = `${key}:${path}`\n        const localHash = serverPathState.get(localKey)\n        const globalHash = serverPathState.get(path)\n        if (\n          (localHash && localHash !== contentHash) ||\n          (globalHash && globalHash !== contentHash)\n        ) {\n          hasChange = true\n          serverPathState.set(key, contentHash)\n          serverPathState.set(path, contentHash)\n        } else {\n          if (!localHash) {\n            serverPathState.set(key, contentHash)\n          }\n          if (!globalHash) {\n            serverPathState.set(path, contentHash)\n          }\n        }\n      }\n\n      if (!hasChange) {\n        return false\n      }\n    }\n\n    resetFetch()\n\n    const hasAppPaths = writtenEndpoint.serverPaths.some(({ path: p }) =>\n      p.startsWith('server/app')\n    )\n\n    if (hasAppPaths) {\n      deleteFromRequireCache(\n        require.resolve(\n          'next/dist/compiled/next-server/app-page-turbo.runtime.dev.js'\n        )\n      )\n      deleteFromRequireCache(\n        require.resolve(\n          'next/dist/compiled/next-server/app-page-turbo-experimental.runtime.dev.js'\n        )\n      )\n    }\n\n    const serverPaths = writtenEndpoint.serverPaths.map(({ path: p }) =>\n      join(distDir, p)\n    )\n\n    for (const file of serverPaths) {\n      clearModuleContext(file)\n      deleteCache(file)\n    }\n\n    return true\n  }\n\n  const buildingIds = new Set()\n\n  const startBuilding: StartBuilding = (id, requestUrl, forceRebuild) => {\n    if (!forceRebuild && readyIds.has(id)) {\n      return () => {}\n    }\n    if (buildingIds.size === 0) {\n      consoleStore.setState(\n        {\n          loading: true,\n          trigger: id,\n          url: requestUrl,\n        } as OutputState,\n        true\n      )\n    }\n    buildingIds.add(id)\n    return function finishBuilding() {\n      if (buildingIds.size === 0) {\n        return\n      }\n      readyIds.add(id)\n      buildingIds.delete(id)\n      if (buildingIds.size === 0) {\n        hmrEventHappened = false\n        consoleStore.setState(\n          {\n            loading: false,\n          } as OutputState,\n          true\n        )\n      }\n    }\n  }\n\n  let hmrEventHappened = false\n  let hmrHash = 0\n\n  const clients = new Set<ws>()\n  const clientStates = new WeakMap<ws, ClientState>()\n\n  function sendToClient(client: ws, payload: HMR_ACTION_TYPES) {\n    client.send(JSON.stringify(payload))\n  }\n\n  function sendEnqueuedMessages() {\n    for (const [, issueMap] of currentEntryIssues) {\n      if (\n        [...issueMap.values()].filter((i) => i.severity !== 'warning').length >\n        0\n      ) {\n        // During compilation errors we want to delay the HMR events until errors are fixed\n        return\n      }\n    }\n\n    for (const client of clients) {\n      const state = clientStates.get(client)\n      if (!state) {\n        continue\n      }\n\n      for (const [, issueMap] of state.clientIssues) {\n        if (\n          [...issueMap.values()].filter((i) => i.severity !== 'warning')\n            .length > 0\n        ) {\n          // During compilation errors we want to delay the HMR events until errors are fixed\n          return\n        }\n      }\n\n      for (const payload of state.hmrPayloads.values()) {\n        sendToClient(client, payload)\n      }\n      state.hmrPayloads.clear()\n\n      if (state.turbopackUpdates.length > 0) {\n        sendToClient(client, {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: state.turbopackUpdates,\n        })\n        state.turbopackUpdates.length = 0\n      }\n    }\n  }\n  const sendEnqueuedMessagesDebounce = debounce(sendEnqueuedMessages, 2)\n\n  const sendHmr: SendHmr = (id: string, payload: HMR_ACTION_TYPES) => {\n    for (const client of clients) {\n      clientStates.get(client)?.hmrPayloads.set(id, payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  function sendTurbopackMessage(payload: TurbopackUpdate) {\n    // TODO(PACK-2049): For some reason we end up emitting hundreds of issues messages on bigger apps,\n    //   a lot of which are duplicates.\n    //   They are currently not handled on the client at all, so might as well not send them for now.\n    payload.diagnostics = []\n    payload.issues = []\n\n    for (const client of clients) {\n      clientStates.get(client)?.turbopackUpdates.push(payload)\n    }\n\n    hmrEventHappened = true\n    sendEnqueuedMessagesDebounce()\n  }\n\n  async function subscribeToChanges(\n    key: EntryKey,\n    includeIssues: boolean,\n    endpoint: Endpoint,\n    makePayload: (\n      change: TurbopackResult,\n      hash: string\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void,\n    onError?: (\n      error: Error\n    ) => Promise<HMR_ACTION_TYPES> | HMR_ACTION_TYPES | void\n  ) {\n    if (changeSubscriptions.has(key)) {\n      return\n    }\n\n    const { side } = splitEntryKey(key)\n\n    const changedPromise = endpoint[`${side}Changed`](includeIssues)\n    changeSubscriptions.set(key, changedPromise)\n    try {\n      const changed = await changedPromise\n\n      for await (const change of changed) {\n        processIssues(currentEntryIssues, key, change, false, true)\n        // TODO: Get an actual content hash from Turbopack.\n        const payload = await makePayload(change, String(++hmrHash))\n        if (payload) {\n          sendHmr(key, payload)\n        }\n      }\n    } catch (e) {\n      changeSubscriptions.delete(key)\n      const payload = await onError?.(e as Error)\n      if (payload) {\n        sendHmr(key, payload)\n      }\n      return\n    }\n    changeSubscriptions.delete(key)\n  }\n\n  async function unsubscribeFromChanges(key: EntryKey) {\n    const subscription = await changeSubscriptions.get(key)\n    if (subscription) {\n      await subscription.return?.()\n      changeSubscriptions.delete(key)\n    }\n    currentEntryIssues.delete(key)\n  }\n\n  async function subscribeToHmrEvents(client: ws, id: string) {\n    const key = getEntryKey('assets', 'client', id)\n    if (!hasEntrypointForKey(currentEntrypoints, key, assetMapper)) {\n      // maybe throw an error / force the client to reload?\n      return\n    }\n\n    const state = clientStates.get(client)\n    if (!state || state.subscriptions.has(id)) {\n      return\n    }\n\n    const subscription = project!.hmrEvents(id)\n    state.subscriptions.set(id, subscription)\n\n    // The subscription will always emit once, which is the initial\n    // computation. This is not a change, so swallow it.\n    try {\n      await subscription.next()\n\n      for await (const data of subscription) {\n        processIssues(state.clientIssues, key, data, false, true)\n        if (data.type !== 'issues') {\n          sendTurbopackMessage(data)\n        }\n      }\n    } catch (e) {\n      // The client might be using an HMR session from a previous server, tell them\n      // to fully reload the page to resolve the issue. We can't use\n      // `hotReloader.send` since that would force every connected client to\n      // reload, only this client is out of date.\n      const reloadAction: ReloadPageAction = {\n        action: HMR_ACTIONS_SENT_TO_BROWSER.RELOAD_PAGE,\n        data: `error in HMR event subscription for ${id}: ${e}`,\n      }\n      sendToClient(client, reloadAction)\n      client.close()\n      return\n    }\n  }\n\n  function unsubscribeFromHmrEvents(client: ws, id: string) {\n    const state = clientStates.get(client)\n    if (!state) {\n      return\n    }\n\n    const subscription = state.subscriptions.get(id)\n    subscription?.return!()\n\n    const key = getEntryKey('assets', 'client', id)\n    state.clientIssues.delete(key)\n  }\n\n  async function handleEntrypointsSubscription() {\n    for await (const entrypoints of entrypointsSubscription) {\n      if (!currentEntriesHandlingResolve) {\n        currentEntriesHandling = new Promise(\n          // eslint-disable-next-line no-loop-func\n          (resolve) => (currentEntriesHandlingResolve = resolve)\n        )\n      }\n\n      processTopLevelIssues(currentTopLevelIssues, entrypoints)\n\n      await handleEntrypoints({\n        entrypoints,\n\n        currentEntrypoints,\n\n        currentEntryIssues,\n        manifestLoader,\n        devRewrites: opts.fsChecker.rewrites,\n        productionRewrites: undefined,\n        logErrors: true,\n\n        dev: {\n          assetMapper,\n          changeSubscriptions,\n          clients,\n          clientStates,\n          serverFields,\n\n          hooks: {\n            handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n              currentWrittenEntrypoints.set(id, result)\n              return clearRequireCache(id, result, { force: forceDeleteCache })\n            },\n            propagateServerField: propagateServerField.bind(null, opts),\n            sendHmr,\n            startBuilding,\n            subscribeToChanges,\n            unsubscribeFromChanges,\n            unsubscribeFromHmrEvents,\n          },\n        },\n      })\n\n      currentEntriesHandlingResolve!()\n      currentEntriesHandlingResolve = undefined\n    }\n  }\n\n  await mkdir(join(distDir, 'server'), { recursive: true })\n  await mkdir(join(distDir, 'static', buildId), { recursive: true })\n  await writeFile(\n    join(distDir, 'package.json'),\n    JSON.stringify(\n      {\n        type: 'commonjs',\n      },\n      null,\n      2\n    )\n  )\n\n  const middlewares = [\n    getOverlayMiddleware(project, projectPath),\n    getSourceMapMiddleware(project),\n    getNextErrorFeedbackMiddleware(opts.telemetry),\n    getDevOverlayFontMiddleware(),\n    getDisableDevIndicatorMiddleware(),\n  ]\n\n  const versionInfoPromise = getVersionInfo()\n\n  let devtoolsFrontendUrl: string | undefined\n  const nodeDebugType = getNodeDebugType()\n  if (nodeDebugType) {\n    const debugPort = process.debugPort\n    let debugInfo\n    try {\n      // It requires to use 127.0.0.1 instead of localhost for server-side fetching.\n      const debugInfoList = await fetch(\n        `http://127.0.0.1:${debugPort}/json/list`\n      ).then((res) => res.json())\n      debugInfo = debugInfoList[0]\n    } catch {}\n    if (debugInfo) {\n      devtoolsFrontendUrl = debugInfo.devtoolsFrontendUrl\n    }\n  }\n\n  const hotReloader: NextJsHotReloaderInterface = {\n    turbopackProject: project,\n    activeWebpackConfigs: undefined,\n    serverStats: null,\n    edgeServerStats: null,\n    async run(req, res, _parsedUrl) {\n      // intercept page chunks request and ensure them with turbopack\n      if (req.url?.startsWith('/_next/static/chunks/pages/')) {\n        const params = matchNextPageBundleRequest(req.url)\n\n        if (params) {\n          const decodedPagePath = `/${params.path\n            .map((param: string) => decodeURIComponent(param))\n            .join('/')}`\n\n          const denormalizedPagePath = denormalizePagePath(decodedPagePath)\n\n          await hotReloader\n            .ensurePage({\n              page: denormalizedPagePath,\n              clientOnly: false,\n              definition: undefined,\n              url: req.url,\n            })\n            .catch(console.error)\n        }\n      }\n\n      for (const middleware of middlewares) {\n        let calledNext = false\n\n        await middleware(req, res, () => {\n          calledNext = true\n        })\n\n        if (!calledNext) {\n          return { finished: true }\n        }\n      }\n\n      // Request was not finished.\n      return { finished: undefined }\n    },\n\n    // TODO: Figure out if socket type can match the NextJsHotReloaderInterface\n    onHMR(req, socket: Socket, head, onUpgrade) {\n      wsServer.handleUpgrade(req, socket, head, (client) => {\n        onUpgrade(client)\n        const clientIssues: EntryIssuesMap = new Map()\n        const subscriptions: Map<string, AsyncIterator<any>> = new Map()\n\n        clients.add(client)\n        clientStates.set(client, {\n          clientIssues,\n          hmrPayloads: new Map(),\n          turbopackUpdates: [],\n          subscriptions,\n        })\n\n        client.on('close', () => {\n          // Remove active subscriptions\n          for (const subscription of subscriptions.values()) {\n            subscription.return?.()\n          }\n          clientStates.delete(client)\n          clients.delete(client)\n        })\n\n        client.addEventListener('message', ({ data }) => {\n          const parsedData = JSON.parse(\n            typeof data !== 'string' ? data.toString() : data\n          )\n\n          // Next.js messages\n          switch (parsedData.event) {\n            case 'span-end': {\n              hotReloaderSpan.manualTraceChild(\n                parsedData.spanName,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                parsedData.attributes\n              )\n              break\n            }\n            case 'client-hmr-latency': // { id, startTime, endTime, page, updatedModules, isPageHidden }\n              hotReloaderSpan.manualTraceChild(\n                parsedData.event,\n                msToNs(parsedData.startTime),\n                msToNs(parsedData.endTime),\n                {\n                  updatedModules: parsedData.updatedModules,\n                  page: parsedData.page,\n                  isPageHidden: parsedData.isPageHidden,\n                }\n              )\n              break\n            case 'client-error': // { errorCount, clientId }\n            case 'client-warning': // { warningCount, clientId }\n            case 'client-success': // { clientId }\n            case 'server-component-reload-page': // { clientId }\n            case 'client-reload-page': // { clientId }\n            case 'client-removed-page': // { page }\n            case 'client-full-reload': // { stackTrace, hadRuntimeError }\n              const { hadRuntimeError, dependencyChain } = parsedData\n              if (hadRuntimeError) {\n                Log.warn(FAST_REFRESH_RUNTIME_RELOAD)\n              }\n              if (\n                Array.isArray(dependencyChain) &&\n                typeof dependencyChain[0] === 'string'\n              ) {\n                const cleanedModulePath = dependencyChain[0]\n                  .replace(/^\\[project\\]/, '.')\n                  .replace(/ \\[.*\\] \\(.*\\)$/, '')\n                Log.warn(\n                  `Fast Refresh had to perform a full reload when ${cleanedModulePath} changed. Read more: https://nextjs.org/docs/messages/fast-refresh-reload`\n                )\n              }\n              break\n            case 'client-added-page':\n              // TODO\n              break\n\n            default:\n              // Might be a Turbopack message...\n              if (!parsedData.type) {\n                throw new Error(`unrecognized HMR message \"${data}\"`)\n              }\n          }\n\n          // Turbopack messages\n          switch (parsedData.type) {\n            case 'turbopack-subscribe':\n              subscribeToHmrEvents(client, parsedData.path)\n              break\n\n            case 'turbopack-unsubscribe':\n              unsubscribeFromHmrEvents(client, parsedData.path)\n              break\n\n            default:\n              if (!parsedData.event) {\n                throw new Error(`unrecognized Turbopack HMR message \"${data}\"`)\n              }\n          }\n        })\n\n        const turbopackConnected: TurbopackConnectedAction = {\n          action: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: { sessionId },\n        }\n        sendToClient(client, turbopackConnected)\n\n        const errors: CompilationError[] = []\n\n        for (const entryIssues of currentEntryIssues.values()) {\n          for (const issue of entryIssues.values()) {\n            if (issue.severity !== 'warning') {\n              errors.push({\n                message: formatIssue(issue),\n              })\n            } else {\n              printNonFatalIssue(issue)\n            }\n          }\n        }\n\n        if (devIndicatorServerState.disabledUntil < Date.now()) {\n          devIndicatorServerState.disabledUntil = 0\n        }\n\n        ;(async function () {\n          const versionInfo = await versionInfoPromise\n\n          const sync: SyncAction = {\n            action: HMR_ACTIONS_SENT_TO_BROWSER.SYNC,\n            errors,\n            warnings: [],\n            hash: '',\n            versionInfo,\n            debug: {\n              devtoolsFrontendUrl,\n            },\n            devIndicator: devIndicatorServerState,\n          }\n\n          sendToClient(client, sync)\n        })()\n      })\n    },\n\n    send(action) {\n      const payload = JSON.stringify(action)\n      for (const client of clients) {\n        client.send(payload)\n      }\n    },\n\n    setHmrServerError(_error) {\n      // Not implemented yet.\n    },\n    clearHmrServerError() {\n      // Not implemented yet.\n    },\n    async start() {},\n    async getCompilationErrors(page) {\n      const appEntryKey = getEntryKey('app', 'server', page)\n      const pagesEntryKey = getEntryKey('pages', 'server', page)\n\n      const topLevelIssues = currentTopLevelIssues.values()\n\n      const thisEntryIssues =\n        currentEntryIssues.get(appEntryKey) ??\n        currentEntryIssues.get(pagesEntryKey)\n\n      if (thisEntryIssues !== undefined && thisEntryIssues.size > 0) {\n        // If there is an error related to the requesting page we display it instead of the first error\n        return [...topLevelIssues, ...thisEntryIssues.values()]\n          .map((issue) => {\n            const formattedIssue = formatIssue(issue)\n            if (issue.severity === 'warning') {\n              printNonFatalIssue(issue)\n              return null\n            } else if (isWellKnownError(issue)) {\n              Log.error(formattedIssue)\n            }\n\n            return new Error(formattedIssue)\n          })\n          .filter((error) => error !== null)\n      }\n\n      // Otherwise, return all errors across pages\n      const errors = []\n      for (const issue of topLevelIssues) {\n        if (issue.severity !== 'warning') {\n          errors.push(new Error(formatIssue(issue)))\n        }\n      }\n      for (const entryIssues of currentEntryIssues.values()) {\n        for (const issue of entryIssues.values()) {\n          if (issue.severity !== 'warning') {\n            const message = formatIssue(issue)\n            errors.push(new Error(message))\n          } else {\n            printNonFatalIssue(issue)\n          }\n        }\n      }\n      return errors\n    },\n    async invalidate({\n      // .env files or tsconfig/jsconfig change\n      reloadAfterInvalidation,\n    }) {\n      if (reloadAfterInvalidation) {\n        for (const [key, entrypoint] of currentWrittenEntrypoints) {\n          clearRequireCache(key, entrypoint, { force: true })\n        }\n\n        await clearAllModuleContexts()\n        this.send({\n          action: HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES,\n          hash: String(++hmrHash),\n        })\n      }\n    },\n    async buildFallbackError() {\n      // Not implemented yet.\n    },\n    async ensurePage({\n      page: inputPage,\n      // Unused parameters\n      // clientOnly,\n      appPaths,\n      definition,\n      isApp,\n      url: requestUrl,\n    }) {\n      // When there is no route definition this is an internal file not a route the user added.\n      // Middleware and instrumentation are handled in turbpack-utils.ts handleEntrypoints instead.\n      if (!definition) {\n        if (inputPage === '/middleware') return\n        if (inputPage === '/src/middleware') return\n        if (inputPage === '/instrumentation') return\n        if (inputPage === '/src/instrumentation') return\n      }\n\n      return hotReloaderSpan\n        .traceChild('ensure-page', {\n          inputPage,\n        })\n        .traceAsyncFn(async () => {\n          if (BLOCKED_PAGES.includes(inputPage) && inputPage !== '/_error') {\n            return\n          }\n\n          await currentEntriesHandling\n\n          // TODO We shouldn't look into the filesystem again. This should use the information from entrypoints\n          let routeDef: Pick<\n            RouteDefinition,\n            'filename' | 'bundlePath' | 'page'\n          > =\n            definition ??\n            (await findPagePathData(\n              projectPath,\n              inputPage,\n              nextConfig.pageExtensions,\n              opts.pagesDir,\n              opts.appDir\n            ))\n\n          // If the route is actually an app page route, then we should have access\n          // to the app route definition, and therefore, the appPaths from it.\n          if (!appPaths && definition && isAppPageRouteDefinition(definition)) {\n            appPaths = definition.appPaths\n          }\n\n          let page = routeDef.page\n          if (appPaths) {\n            const normalizedPage = normalizeAppPath(page)\n\n            // filter out paths that are not exact matches (e.g. catchall)\n            const matchingAppPaths = appPaths.filter(\n              (path) => normalizeAppPath(path) === normalizedPage\n            )\n\n            // the last item in the array is the root page, if there are parallel routes\n            page = matchingAppPaths[matchingAppPaths.length - 1]\n          }\n\n          const pathname = definition?.pathname ?? inputPage\n\n          if (page === '/_error') {\n            let finishBuilding = startBuilding(pathname, requestUrl, false)\n            try {\n              await handlePagesErrorRoute({\n                currentEntryIssues,\n                entrypoints: currentEntrypoints,\n                manifestLoader,\n                devRewrites: opts.fsChecker.rewrites,\n                productionRewrites: undefined,\n                logErrors: true,\n                hooks: {\n                  subscribeToChanges,\n                  handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n                    currentWrittenEntrypoints.set(id, result)\n                    assetMapper.setPathsForKey(id, result.clientPaths)\n                    return clearRequireCache(id, result, {\n                      force: forceDeleteCache,\n                    })\n                  },\n                },\n              })\n            } finally {\n              finishBuilding()\n            }\n            return\n          }\n\n          const isInsideAppDir = routeDef.bundlePath.startsWith('app/')\n          const isEntryMetadataRouteFile = isMetadataRouteFile(\n            routeDef.filename.replace(opts.appDir || '', ''),\n            nextConfig.pageExtensions,\n            true\n          )\n          const normalizedAppPage = isEntryMetadataRouteFile\n            ? normalizedPageToTurbopackStructureRoute(\n                page,\n                extname(routeDef.filename)\n              )\n            : page\n\n          const route = isInsideAppDir\n            ? currentEntrypoints.app.get(normalizedAppPage)\n            : currentEntrypoints.page.get(page)\n\n          if (!route) {\n            // TODO: why is this entry missing in turbopack?\n            if (page === '/middleware') return\n            if (page === '/src/middleware') return\n            if (page === '/instrumentation') return\n            if (page === '/src/instrumentation') return\n\n            throw new PageNotFoundError(`route not found ${page}`)\n          }\n\n          // We don't throw on ensureOpts.isApp === true for page-api\n          // since this can happen when app pages make\n          // api requests to page API routes.\n          if (isApp && route.type === 'page') {\n            throw new Error(`mis-matched route type: isApp && page for ${page}`)\n          }\n\n          const finishBuilding = startBuilding(pathname, requestUrl, false)\n          try {\n            await handleRouteType({\n              dev,\n              page,\n              pathname,\n              route,\n              currentEntryIssues,\n              entrypoints: currentEntrypoints,\n              manifestLoader,\n              readyIds,\n              devRewrites: opts.fsChecker.rewrites,\n              productionRewrites: undefined,\n              logErrors: true,\n\n              hooks: {\n                subscribeToChanges,\n                handleWrittenEndpoint: (id, result, forceDeleteCache) => {\n                  currentWrittenEntrypoints.set(id, result)\n                  assetMapper.setPathsForKey(id, result.clientPaths)\n                  return clearRequireCache(id, result, {\n                    force: forceDeleteCache,\n                  })\n                },\n              },\n            })\n          } finally {\n            finishBuilding()\n          }\n        })\n    },\n    close() {\n      for (const wsClient of clients) {\n        // it's okay to not cleanly close these websocket connections, this is dev\n        wsClient.terminate()\n      }\n      clients.clear()\n    },\n  }\n\n  handleEntrypointsSubscription().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  // Write empty manifests\n  await currentEntriesHandling\n  await manifestLoader.writeManifests({\n    devRewrites: opts.fsChecker.rewrites,\n    productionRewrites: undefined,\n    entrypoints: currentEntrypoints,\n  })\n\n  async function handleProjectUpdates() {\n    for await (const updateMessage of project.updateInfoSubscribe(30)) {\n      switch (updateMessage.updateType) {\n        case 'start': {\n          hotReloader.send({ action: HMR_ACTIONS_SENT_TO_BROWSER.BUILDING })\n          break\n        }\n        case 'end': {\n          sendEnqueuedMessages()\n\n          function addErrors(\n            errorsMap: Map<string, CompilationError>,\n            issues: EntryIssuesMap\n          ) {\n            for (const issueMap of issues.values()) {\n              for (const [key, issue] of issueMap) {\n                if (issue.severity === 'warning') continue\n                if (errorsMap.has(key)) continue\n\n                const message = formatIssue(issue)\n\n                errorsMap.set(key, {\n                  message,\n                  details: issue.detail\n                    ? renderStyledStringToErrorAnsi(issue.detail)\n                    : undefined,\n                })\n              }\n            }\n          }\n\n          const errors = new Map<string, CompilationError>()\n          addErrors(errors, currentEntryIssues)\n\n          for (const client of clients) {\n            const state = clientStates.get(client)\n            if (!state) {\n              continue\n            }\n\n            const clientErrors = new Map(errors)\n            addErrors(clientErrors, state.clientIssues)\n\n            sendToClient(client, {\n              action: HMR_ACTIONS_SENT_TO_BROWSER.BUILT,\n              hash: String(++hmrHash),\n              errors: [...clientErrors.values()],\n              warnings: [],\n            })\n          }\n\n          if (hmrEventHappened) {\n            const time = updateMessage.value.duration\n            const timeMessage =\n              time > 2000 ? `${Math.round(time / 100) / 10}s` : `${time}ms`\n            Log.event(`Compiled in ${timeMessage}`)\n            hmrEventHappened = false\n          }\n          break\n        }\n        default:\n      }\n    }\n  }\n\n  handleProjectUpdates().catch((err) => {\n    console.error(err)\n    process.exit(1)\n  })\n\n  return hotReloader\n}\n"], "names": ["mkdir", "writeFile", "join", "extname", "pathToFileURL", "ws", "store", "consoleStore", "HMR_ACTIONS_SENT_TO_BROWSER", "createDefineEnv", "Log", "getVersionInfo", "matchNextPageBundleRequest", "BLOCKED_PAGES", "getOverlayMiddleware", "getSourceMapMiddleware", "PageNotFoundError", "debounce", "deleteCache", "deleteFromRequireCache", "clearAllModuleContexts", "clearModuleContext", "denormalizePagePath", "trace", "AssetMapper", "handleEntrypoints", "handlePagesErrorRoute", "handleRouteType", "hasEntrypointForKey", "msToNs", "processTopLevelIssues", "printNonFatalIssue", "normalizedPageToTurbopackStructureRoute", "propagateServerField", "TurbopackManifestLoader", "findPagePathData", "getEntry<PERSON>ey", "splitEntryKey", "FAST_REFRESH_RUNTIME_RELOAD", "generateEncryptionKeyBase64", "isAppPageRouteDefinition", "normalizeAppPath", "getNodeDebugType", "isMetadataRouteFile", "setBundlerFindSourceMapImplementation", "getNextErrorFeedbackMiddleware", "formatIssue", "getTurbopackJsConfig", "isPersistentCachingEnabled", "isWellKnownError", "processIssues", "renderStyledStringToErrorAnsi", "getDevOverlayFontMiddleware", "devIndicatorServerState", "getDisableDevIndicatorMiddleware", "wsServer", "Server", "noServer", "isTestMode", "process", "env", "NEXT_TEST_MODE", "__NEXT_TEST_MODE", "DEBUG", "sessionId", "Math", "floor", "Number", "MAX_SAFE_INTEGER", "random", "rewriteTurbopackSources", "projectRoot", "sourceMap", "section", "sections", "map", "i", "sources", "length", "replace", "toString", "getSourceMapFromTurbopack", "project", "sourceURL", "sourceMapJson", "getSourceMapSync", "err", "undefined", "payload", "JSON", "parse", "createHotReloaderTurbopack", "opts", "serverFields", "distDir", "resetFetch", "nextConfig", "dev", "buildId", "dir", "projectPath", "loadBindings", "require", "bindings", "log", "testMode", "hasRewrites", "fs<PERSON><PERSON><PERSON>", "rewrites", "afterFiles", "beforeFiles", "fallback", "hotReloaderSpan", "version", "__NEXT_VERSION", "stop", "<PERSON><PERSON><PERSON>", "isBuild", "clientRouterFilters", "experimental", "clientRouterFilter", "supportedBrowsers", "turbo", "createProject", "rootPath", "turbopack", "root", "outputFileTracingRoot", "jsConfig", "watch", "enable", "pollIntervalMs", "watchOptions", "defineEnv", "isTurbopack", "config", "fetchCacheKeyPrefix", "middlewareMatchers", "previewProps", "prerenderManifest", "preview", "browserslistQuery", "noMangling", "persistentCaching", "memoryLimit", "turbopackMemoryLimit", "bind", "onDevServerCleanup", "onExit", "entrypointsSubscription", "entrypointsSubscribe", "currentWrittenEntrypoints", "Map", "currentEntrypoints", "global", "app", "document", "error", "middleware", "instrumentation", "page", "currentTopLevelIssues", "currentEntryIssues", "manifest<PERSON><PERSON>der", "changeSubscriptions", "serverPathState", "readyIds", "Set", "currentEntriesHandlingResolve", "currentEntriesHandling", "Promise", "resolve", "assetMapper", "clearRequireCache", "key", "writtenEndpoint", "force", "path", "contentHash", "serverPaths", "set", "hasChange", "endsWith", "localKey", "localHash", "get", "globalHash", "hasAppPaths", "some", "p", "startsWith", "file", "buildingIds", "startBuilding", "id", "requestUrl", "forceRebuild", "has", "size", "setState", "loading", "trigger", "url", "add", "finishBuilding", "delete", "hmrEventHappened", "hmrHash", "clients", "clientStates", "WeakMap", "sendToClient", "client", "send", "stringify", "sendEnqueuedMessages", "issueMap", "values", "filter", "severity", "state", "clientIssues", "hmrPayloads", "clear", "turbopackUpdates", "action", "TURBOPACK_MESSAGE", "data", "sendEnqueuedMessagesDebounce", "sendHmr", "sendTurbopackMessage", "diagnostics", "issues", "push", "subscribeToChanges", "includeIssues", "endpoint", "makePayload", "onError", "side", "changedPromise", "changed", "change", "String", "e", "unsubscribeFromChanges", "subscription", "return", "subscribeToHmrEvents", "subscriptions", "hmrEvents", "next", "type", "reloadAction", "RELOAD_PAGE", "close", "unsubscribeFromHmrEvents", "handleEntrypointsSubscription", "entrypoints", "devRewrites", "productionRewrites", "logErrors", "hooks", "handleWrittenEndpoint", "result", "forceDeleteCache", "recursive", "middlewares", "telemetry", "versionInfoPromise", "devtoolsFrontendUrl", "nodeDebugType", "debugPort", "debugInfo", "debugInfoList", "fetch", "then", "res", "json", "hotReloader", "turbopackProject", "activeWebpackConfigs", "serverStats", "edgeServerStats", "run", "req", "_parsedUrl", "params", "decodedPagePath", "param", "decodeURIComponent", "denormalizedPagePath", "ensurePage", "clientOnly", "definition", "catch", "console", "calledNext", "finished", "onHMR", "socket", "head", "onUpgrade", "handleUpgrade", "on", "addEventListener", "parsedData", "event", "manualTraceChild", "spanName", "startTime", "endTime", "attributes", "updatedModules", "isPageHidden", "hadRuntimeError", "dependency<PERSON><PERSON>n", "warn", "Array", "isArray", "cleanedModulePath", "Error", "turbopackConnected", "TURBOPACK_CONNECTED", "errors", "entryIssues", "issue", "message", "disabledUntil", "Date", "now", "versionInfo", "sync", "SYNC", "warnings", "hash", "debug", "devIndicator", "setHmrServerError", "_error", "clearHmrServerError", "start", "getCompilationErrors", "appEntry<PERSON>ey", "pagesEntry<PERSON>ey", "topLevelIssues", "thisEntryIssues", "formattedIssue", "invalidate", "reloadAfterInvalidation", "entrypoint", "SERVER_COMPONENT_CHANGES", "buildFallbackError", "inputPage", "appPaths", "isApp", "<PERSON><PERSON><PERSON><PERSON>", "traceAsyncFn", "includes", "routeDef", "pageExtensions", "pagesDir", "appDir", "normalizedPage", "matchingAppPaths", "pathname", "setPathsFor<PERSON>ey", "clientPaths", "isInsideAppDir", "bundlePath", "isEntryMetadataRouteFile", "filename", "normalizedAppPage", "route", "wsClient", "terminate", "exit", "writeManifests", "handleProjectUpdates", "updateMessage", "updateInfoSubscribe", "updateType", "BUILDING", "addErrors", "errorsMap", "details", "detail", "clientErrors", "BUILT", "time", "value", "duration", "timeMessage", "round"], "mappings": "AACA,SAASA,KAAK,EAAEC,SAAS,QAAQ,cAAa;AAC9C,SAASC,IAAI,EAAEC,OAAO,QAAQ,OAAM;AACpC,SAASC,aAAa,QAAQ,MAAK;AAEnC,OAAOC,QAAQ,wBAAuB;AAGtC,SAASC,SAASC,YAAY,QAAQ,2BAA0B;AAShE,SAASC,2BAA2B,QAAQ,uBAAsB;AASlE,SAASC,eAAe,QAAQ,kBAAiB;AACjD,YAAYC,SAAS,yBAAwB;AAC7C,SACEC,cAAc,EACdC,0BAA0B,QACrB,yBAAwB;AAC/B,SAASC,aAAa,QAAQ,6BAA4B;AAC1D,SACEC,oBAAoB,EACpBC,sBAAsB,QACjB,wEAAuE;AAC9E,SAASC,iBAAiB,QAAQ,yBAAwB;AAC1D,SAASC,QAAQ,QAAQ,WAAU;AACnC,SAASC,WAAW,EAAEC,sBAAsB,QAAQ,kBAAiB;AACrE,SACEC,sBAAsB,EACtBC,kBAAkB,QACb,uBAAsB;AAC7B,SAASC,mBAAmB,QAAQ,mDAAkD;AACtF,SAASC,KAAK,QAAQ,cAAa;AACnC,SACEC,WAAW,EAGXC,iBAAiB,EACjBC,qBAAqB,EACrBC,eAAe,EACfC,mBAAmB,EACnBC,MAAM,EAINC,qBAAqB,EACrBC,kBAAkB,EAClBC,uCAAuC,QAClC,oBAAmB;AAC1B,SACEC,oBAAoB,QAGf,wCAAuC;AAC9C,SAASC,uBAAuB,QAAQ,6CAA4C;AACpF,SAASC,gBAAgB,QAAQ,4BAA2B;AAE5D,SAEEC,WAAW,EACXC,aAAa,QACR,uCAAsC;AAC7C,SAASC,2BAA2B,QAAQ,aAAY;AACxD,SAASC,2BAA2B,QAAQ,wCAAuC;AACnF,SAASC,wBAAwB,QAAQ,iDAAgD;AACzF,SAASC,gBAAgB,QAAQ,0CAAyC;AAC1E,SAASC,gBAAgB,QAAQ,eAAc;AAC/C,SAASC,mBAAmB,QAAQ,uCAAsC;AAC1E,SACEC,qCAAqC,QAEhC,yBAAwB;AAC/B,SAASC,8BAA8B,QAAQ,sFAAqF;AACpI,SACEC,WAAW,EACXC,oBAAoB,EACpBC,0BAA0B,EAC1BC,gBAAgB,EAChBC,aAAa,EACbC,6BAA6B,QAGxB,mCAAkC;AACzC,SAASC,2BAA2B,QAAQ,iFAAgF;AAC5H,SAASC,uBAAuB,QAAQ,+BAA8B;AACtE,SAASC,gCAAgC,QAAQ,6BAA4B;AAC7E,2DAA2D;AAE3D,MAAMC,WAAW,IAAIlD,GAAGmD,MAAM,CAAC;IAAEC,UAAU;AAAK;AAChD,MAAMC,aAAa,CAAC,CAClBC,CAAAA,QAAQC,GAAG,CAACC,cAAc,IAC1BF,QAAQC,GAAG,CAACE,gBAAgB,IAC5BH,QAAQC,GAAG,CAACG,KAAK,AAAD;AAGlB,MAAMC,YAAYC,KAAKC,KAAK,CAACC,OAAOC,gBAAgB,GAAGH,KAAKI,MAAM;AAElE;;CAEC,GACD,SAASC,wBACPC,WAAmB,EACnBC,SAAiC;IAEjC,IAAI,cAAcA,WAAW;QAC3B,KAAK,MAAMC,WAAWD,UAAUE,QAAQ,CAAE;YACxCJ,wBAAwBC,aAAaE,QAAQE,GAAG;QAClD;IACF,OAAO;QACL,IAAK,IAAIC,IAAI,GAAGA,IAAIJ,UAAUK,OAAO,CAACC,MAAM,EAAEF,IAAK;YACjDJ,UAAUK,OAAO,CAACD,EAAE,GAAGxE,cACrBF,KACEqE,aACAC,UAAUK,OAAO,CAACD,EAAE,CAACG,OAAO,CAAC,+BAA+B,MAE9DC,QAAQ;QACZ;IACF;AACF;AAEA,SAASC,0BACPC,OAAgB,EAChBX,WAAmB,EACnBY,SAAiB;IAEjB,IAAIC,gBAA+B;IAEnC,IAAI;QACFA,gBAAgBF,QAAQG,gBAAgB,CAACF;IAC3C,EAAE,OAAOG,KAAK,CAAC;IAEf,IAAIF,kBAAkB,MAAM;QAC1B,OAAOG;IACT,OAAO;QACL,MAAMC,UAAkCC,KAAKC,KAAK,CAACN;QACnD,2EAA2E;QAC3E,4EAA4E;QAC5Ed,wBAAwBC,aAAaiB;QACrC,OAAOA;IACT;AACF;AAEA,OAAO,eAAeG,2BACpBC,IAAe,EACfC,YAA0B,EAC1BC,OAAe,EACfC,UAAsB;QAoDhBH,4BAQgBI,0BAuBLJ;IAjFjB,MAAMK,MAAM;IACZ,MAAMC,UAAU;IAChB,MAAM,EAAEF,UAAU,EAAEG,KAAKC,WAAW,EAAE,GAAGR;IAEzC,MAAM,EAAES,YAAY,EAAE,GACpBC,QAAQ;IAEV,IAAIC,WAAW,MAAMF;IAErB,iGAAiG;IACjG,yGAAyG;IACzG,IAAI3C,YAAY;QACd4C,QAAQ,WAAWE,GAAG,CAAC,8BAA8B;YACnDL,KAAKC;YACLK,UAAU/C;QACZ;IACF;IAEA,MAAMgD,cACJd,KAAKe,SAAS,CAACC,QAAQ,CAACC,UAAU,CAAC/B,MAAM,GAAG,KAC5Cc,KAAKe,SAAS,CAACC,QAAQ,CAACE,WAAW,CAAChC,MAAM,GAAG,KAC7Cc,KAAKe,SAAS,CAACC,QAAQ,CAACG,QAAQ,CAACjC,MAAM,GAAG;IAE5C,MAAMkC,kBAAkBzF,MAAM,gBAAgBgE,WAAW;QACvD0B,SAAStD,QAAQC,GAAG,CAACsD,cAAc;IACrC;IACA,8FAA8F;IAC9F,wCAAwC;IACxCF,gBAAgBG,IAAI;IAEpB,MAAMC,gBAAgB,MAAM7E,4BAA4B;QACtD8E,SAAS;QACTvB;IACF;IAEA,kBAAkB;IAClB,IAAIwB;IACJ,IAAItB,WAAWuB,YAAY,CAACC,kBAAkB,EAAE;IAC9C,oEAAoE;IACtE;IAEA,iEAAiE;IACjE,MAAMC,oBAAoB;QACxB;KACD;IAED,MAAMvC,UAAU,MAAMqB,SAASmB,KAAK,CAACC,aAAa,CAChD;QACEvB,aAAaA;QACbwB,UACEhC,EAAAA,6BAAAA,KAAKI,UAAU,CAAC6B,SAAS,qBAAzBjC,2BAA2BkC,IAAI,KAC/BlC,KAAKI,UAAU,CAAC+B,qBAAqB,IACrC3B;QACFN;QACAE,YAAYJ,KAAKI,UAAU;QAC3BgC,UAAU,MAAMjF,qBAAqBqD,aAAaJ;QAClDiC,OAAO;YACLC,QAAQjC;YACRkC,cAAc,GAAEnC,2BAAAA,WAAWoC,YAAY,qBAAvBpC,yBAAyBmC,cAAc;QACzD;QACAlC;QACArC,KAAKD,QAAQC,GAAG;QAChByE,WAAW5H,gBAAgB;YACzB6H,aAAa;YACbhB;YACAiB,QAAQvC;YACRC;YACAH;YACA0C,qBAAqB5C,KAAKI,UAAU,CAACuB,YAAY,CAACiB,mBAAmB;YACrE9B;YACA,kBAAkB;YAClB+B,oBAAoBlD;QACtB;QACAW;QACAkB;QACAsB,cAAc9C,KAAKe,SAAS,CAACgC,iBAAiB,CAACC,OAAO;QACtDC,mBAAmBpB,kBAAkBvH,IAAI,CAAC;QAC1C4I,YAAY;IACd,GACA;QACEC,mBAAmB/F,2BAA2B4C,KAAKI,UAAU;QAC7DgD,WAAW,GAAEpD,gCAAAA,KAAKI,UAAU,CAACuB,YAAY,qBAA5B3B,8BAA8BqD,oBAAoB;IACjE;IAEFrG,sCACEqC,0BAA0BiE,IAAI,CAAC,MAAMhE,SAASkB;IAEhDR,KAAKuD,kBAAkB,oBAAvBvD,KAAKuD,kBAAkB,MAAvBvD,MAA0B;QACxBhD,sCAAsC,IAAM2C;QAC5C,MAAML,QAAQkE,MAAM;IACtB;IACA,MAAMC,0BAA0BnE,QAAQoE,oBAAoB;IAE5D,MAAMC,4BAA4D,IAAIC;IACtE,MAAMC,qBAAkC;QACtCC,QAAQ;YACNC,KAAKpE;YACLqE,UAAUrE;YACVsE,OAAOtE;YAEPuE,YAAYvE;YACZwE,iBAAiBxE;QACnB;QAEAyE,MAAM,IAAIR;QACVG,KAAK,IAAIH;IACX;IAEA,MAAMS,wBAA2C,IAAIT;IACrD,MAAMU,qBAAqC,IAAIV;IAE/C,MAAMW,iBAAiB,IAAIjI,wBAAwB;QACjDgE;QACAJ;QACAsB;IACF;IAEA,eAAe;IACf,MAAMgD,sBAA2C,IAAIZ;IACrD,MAAMa,kBAAkB,IAAIb;IAC5B,MAAMc,WAAqB,IAAIC;IAC/B,IAAIC;IACJ,IAAIC,yBAAyB,IAAIC,QAC/B,CAACC,UAAaH,gCAAgCG;IAGhD,MAAMC,cAAc,IAAIpJ;IAExB,SAASqJ,kBACPC,GAAa,EACbC,eAAgC,EAChC,EACEC,KAAK,EAIN,GAAG,CAAC,CAAC;QAEN,IAAIA,OAAO;YACT,KAAK,MAAM,EAAEC,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/Dd,gBAAgBe,GAAG,CAACH,MAAMC;YAC5B;QACF,OAAO;YACL,8CAA8C;YAC9C,IAAIG,YAAY;YAChB,KAAK,MAAM,EAAEJ,IAAI,EAAEC,WAAW,EAAE,IAAIH,gBAAgBI,WAAW,CAAE;gBAC/D,wBAAwB;gBACxB,IAAIF,KAAKK,QAAQ,CAAC,SAAS;gBAC3B,MAAMC,WAAW,GAAGT,IAAI,CAAC,EAAEG,MAAM;gBACjC,MAAMO,YAAYnB,gBAAgBoB,GAAG,CAACF;gBACtC,MAAMG,aAAarB,gBAAgBoB,GAAG,CAACR;gBACvC,IACE,AAACO,aAAaA,cAAcN,eAC3BQ,cAAcA,eAAeR,aAC9B;oBACAG,YAAY;oBACZhB,gBAAgBe,GAAG,CAACN,KAAKI;oBACzBb,gBAAgBe,GAAG,CAACH,MAAMC;gBAC5B,OAAO;oBACL,IAAI,CAACM,WAAW;wBACdnB,gBAAgBe,GAAG,CAACN,KAAKI;oBAC3B;oBACA,IAAI,CAACQ,YAAY;wBACfrB,gBAAgBe,GAAG,CAACH,MAAMC;oBAC5B;gBACF;YACF;YAEA,IAAI,CAACG,WAAW;gBACd,OAAO;YACT;QACF;QAEAtF;QAEA,MAAM4F,cAAcZ,gBAAgBI,WAAW,CAACS,IAAI,CAAC,CAAC,EAAEX,MAAMY,CAAC,EAAE,GAC/DA,EAAEC,UAAU,CAAC;QAGf,IAAIH,aAAa;YACfxK,uBACEmF,QAAQqE,OAAO,CACb;YAGJxJ,uBACEmF,QAAQqE,OAAO,CACb;QAGN;QAEA,MAAMQ,cAAcJ,gBAAgBI,WAAW,CAACxG,GAAG,CAAC,CAAC,EAAEsG,MAAMY,CAAC,EAAE,GAC9D3L,KAAK4F,SAAS+F;QAGhB,KAAK,MAAME,QAAQZ,YAAa;YAC9B9J,mBAAmB0K;YACnB7K,YAAY6K;QACd;QAEA,OAAO;IACT;IAEA,MAAMC,cAAc,IAAIzB;IAExB,MAAM0B,gBAA+B,CAACC,IAAIC,YAAYC;QACpD,IAAI,CAACA,gBAAgB9B,SAAS+B,GAAG,CAACH,KAAK;YACrC,OAAO,KAAO;QAChB;QACA,IAAIF,YAAYM,IAAI,KAAK,GAAG;YAC1B/L,aAAagM,QAAQ,CACnB;gBACEC,SAAS;gBACTC,SAASP;gBACTQ,KAAKP;YACP,GACA;QAEJ;QACAH,YAAYW,GAAG,CAACT;QAChB,OAAO,SAASU;YACd,IAAIZ,YAAYM,IAAI,KAAK,GAAG;gBAC1B;YACF;YACAhC,SAASqC,GAAG,CAACT;YACbF,YAAYa,MAAM,CAACX;YACnB,IAAIF,YAAYM,IAAI,KAAK,GAAG;gBAC1BQ,mBAAmB;gBACnBvM,aAAagM,QAAQ,CACnB;oBACEC,SAAS;gBACX,GACA;YAEJ;QACF;IACF;IAEA,IAAIM,mBAAmB;IACvB,IAAIC,UAAU;IAEd,MAAMC,UAAU,IAAIzC;IACpB,MAAM0C,eAAe,IAAIC;IAEzB,SAASC,aAAaC,MAAU,EAAE5H,OAAyB;QACzD4H,OAAOC,IAAI,CAAC5H,KAAK6H,SAAS,CAAC9H;IAC7B;IAEA,SAAS+H;QACP,KAAK,MAAM,GAAGC,SAAS,IAAItD,mBAAoB;YAC7C,IACE;mBAAIsD,SAASC,MAAM;aAAG,CAACC,MAAM,CAAC,CAAC9I,IAAMA,EAAE+I,QAAQ,KAAK,WAAW7I,MAAM,GACrE,GACA;gBACA,mFAAmF;gBACnF;YACF;QACF;QAEA,KAAK,MAAMsI,UAAUJ,QAAS;YAC5B,MAAMY,QAAQX,aAAaxB,GAAG,CAAC2B;YAC/B,IAAI,CAACQ,OAAO;gBACV;YACF;YAEA,KAAK,MAAM,GAAGJ,SAAS,IAAII,MAAMC,YAAY,CAAE;gBAC7C,IACE;uBAAIL,SAASC,MAAM;iBAAG,CAACC,MAAM,CAAC,CAAC9I,IAAMA,EAAE+I,QAAQ,KAAK,WACjD7I,MAAM,GAAG,GACZ;oBACA,mFAAmF;oBACnF;gBACF;YACF;YAEA,KAAK,MAAMU,WAAWoI,MAAME,WAAW,CAACL,MAAM,GAAI;gBAChDN,aAAaC,QAAQ5H;YACvB;YACAoI,MAAME,WAAW,CAACC,KAAK;YAEvB,IAAIH,MAAMI,gBAAgB,CAAClJ,MAAM,GAAG,GAAG;gBACrCqI,aAAaC,QAAQ;oBACnBa,QAAQzN,4BAA4B0N,iBAAiB;oBACrDC,MAAMP,MAAMI,gBAAgB;gBAC9B;gBACAJ,MAAMI,gBAAgB,CAAClJ,MAAM,GAAG;YAClC;QACF;IACF;IACA,MAAMsJ,+BAA+BnN,SAASsM,sBAAsB;IAEpE,MAAMc,UAAmB,CAACnC,IAAY1G;QACpC,KAAK,MAAM4H,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAaxB,GAAG,CAAC2B,4BAAjBH,kBAA0Ba,WAAW,CAAC1C,GAAG,CAACc,IAAI1G;QAChD;QAEAsH,mBAAmB;QACnBsB;IACF;IAEA,SAASE,qBAAqB9I,OAAwB;QACpD,kGAAkG;QAClG,mCAAmC;QACnC,iGAAiG;QACjGA,QAAQ+I,WAAW,GAAG,EAAE;QACxB/I,QAAQgJ,MAAM,GAAG,EAAE;QAEnB,KAAK,MAAMpB,UAAUJ,QAAS;gBAC5BC;aAAAA,oBAAAA,aAAaxB,GAAG,CAAC2B,4BAAjBH,kBAA0Be,gBAAgB,CAACS,IAAI,CAACjJ;QAClD;QAEAsH,mBAAmB;QACnBsB;IACF;IAEA,eAAeM,mBACb5D,GAAa,EACb6D,aAAsB,EACtBC,QAAkB,EAClBC,WAGwD,EACxDC,OAEwD;QAExD,IAAI1E,oBAAoBiC,GAAG,CAACvB,MAAM;YAChC;QACF;QAEA,MAAM,EAAEiE,IAAI,EAAE,GAAG1M,cAAcyI;QAE/B,MAAMkE,iBAAiBJ,QAAQ,CAAC,GAAGG,KAAK,OAAO,CAAC,CAAC,CAACJ;QAClDvE,oBAAoBgB,GAAG,CAACN,KAAKkE;QAC7B,IAAI;YACF,MAAMC,UAAU,MAAMD;YAEtB,WAAW,MAAME,UAAUD,QAAS;gBAClC/L,cAAcgH,oBAAoBY,KAAKoE,QAAQ,OAAO;gBACtD,mDAAmD;gBACnD,MAAM1J,UAAU,MAAMqJ,YAAYK,QAAQC,OAAO,EAAEpC;gBACnD,IAAIvH,SAAS;oBACX6I,QAAQvD,KAAKtF;gBACf;YACF;QACF,EAAE,OAAO4J,GAAG;YACVhF,oBAAoByC,MAAM,CAAC/B;YAC3B,MAAMtF,UAAU,OAAMsJ,2BAAAA,QAAUM;YAChC,IAAI5J,SAAS;gBACX6I,QAAQvD,KAAKtF;YACf;YACA;QACF;QACA4E,oBAAoByC,MAAM,CAAC/B;IAC7B;IAEA,eAAeuE,uBAAuBvE,GAAa;QACjD,MAAMwE,eAAe,MAAMlF,oBAAoBqB,GAAG,CAACX;QACnD,IAAIwE,cAAc;YAChB,OAAMA,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;YACNlF,oBAAoByC,MAAM,CAAC/B;QAC7B;QACAZ,mBAAmB2C,MAAM,CAAC/B;IAC5B;IAEA,eAAe0E,qBAAqBpC,MAAU,EAAElB,EAAU;QACxD,MAAMpB,MAAM1I,YAAY,UAAU,UAAU8J;QAC5C,IAAI,CAACtK,oBAAoB6H,oBAAoBqB,KAAKF,cAAc;YAC9D,qDAAqD;YACrD;QACF;QAEA,MAAMgD,QAAQX,aAAaxB,GAAG,CAAC2B;QAC/B,IAAI,CAACQ,SAASA,MAAM6B,aAAa,CAACpD,GAAG,CAACH,KAAK;YACzC;QACF;QAEA,MAAMoD,eAAepK,QAASwK,SAAS,CAACxD;QACxC0B,MAAM6B,aAAa,CAACrE,GAAG,CAACc,IAAIoD;QAE5B,+DAA+D;QAC/D,oDAAoD;QACpD,IAAI;YACF,MAAMA,aAAaK,IAAI;YAEvB,WAAW,MAAMxB,QAAQmB,aAAc;gBACrCpM,cAAc0K,MAAMC,YAAY,EAAE/C,KAAKqD,MAAM,OAAO;gBACpD,IAAIA,KAAKyB,IAAI,KAAK,UAAU;oBAC1BtB,qBAAqBH;gBACvB;YACF;QACF,EAAE,OAAOiB,GAAG;YACV,6EAA6E;YAC7E,8DAA8D;YAC9D,sEAAsE;YACtE,2CAA2C;YAC3C,MAAMS,eAAiC;gBACrC5B,QAAQzN,4BAA4BsP,WAAW;gBAC/C3B,MAAM,CAAC,oCAAoC,EAAEjC,GAAG,EAAE,EAAEkD,GAAG;YACzD;YACAjC,aAAaC,QAAQyC;YACrBzC,OAAO2C,KAAK;YACZ;QACF;IACF;IAEA,SAASC,yBAAyB5C,MAAU,EAAElB,EAAU;QACtD,MAAM0B,QAAQX,aAAaxB,GAAG,CAAC2B;QAC/B,IAAI,CAACQ,OAAO;YACV;QACF;QAEA,MAAM0B,eAAe1B,MAAM6B,aAAa,CAAChE,GAAG,CAACS;QAC7CoD,gCAAAA,aAAcC,MAAM;QAEpB,MAAMzE,MAAM1I,YAAY,UAAU,UAAU8J;QAC5C0B,MAAMC,YAAY,CAAChB,MAAM,CAAC/B;IAC5B;IAEA,eAAemF;QACb,WAAW,MAAMC,eAAe7G,wBAAyB;YACvD,IAAI,CAACmB,+BAA+B;gBAClCC,yBAAyB,IAAIC,QAC3B,wCAAwC;gBACxC,CAACC,UAAaH,gCAAgCG;YAElD;YAEA7I,sBAAsBmI,uBAAuBiG;YAE7C,MAAMzO,kBAAkB;gBACtByO;gBAEAzG;gBAEAS;gBACAC;gBACAgG,aAAavK,KAAKe,SAAS,CAACC,QAAQ;gBACpCwJ,oBAAoB7K;gBACpB8K,WAAW;gBAEXpK,KAAK;oBACH2E;oBACAR;oBACA4C;oBACAC;oBACApH;oBAEAyK,OAAO;wBACLC,uBAAuB,CAACrE,IAAIsE,QAAQC;4BAClClH,0BAA0B6B,GAAG,CAACc,IAAIsE;4BAClC,OAAO3F,kBAAkBqB,IAAIsE,QAAQ;gCAAExF,OAAOyF;4BAAiB;wBACjE;wBACAxO,sBAAsBA,qBAAqBiH,IAAI,CAAC,MAAMtD;wBACtDyI;wBACApC;wBACAyC;wBACAW;wBACAW;oBACF;gBACF;YACF;YAEAxF;YACAA,gCAAgCjF;QAClC;IACF;IAEA,MAAMvF,MAAME,KAAK4F,SAAS,WAAW;QAAE4K,WAAW;IAAK;IACvD,MAAM1Q,MAAME,KAAK4F,SAAS,UAAUI,UAAU;QAAEwK,WAAW;IAAK;IAChE,MAAMzQ,UACJC,KAAK4F,SAAS,iBACdL,KAAK6H,SAAS,CACZ;QACEsC,MAAM;IACR,GACA,MACA;IAIJ,MAAMe,cAAc;QAClB7P,qBAAqBoE,SAASkB;QAC9BrF,uBAAuBmE;QACvBrC,+BAA+B+C,KAAKgL,SAAS;QAC7CxN;QACAE;KACD;IAED,MAAMuN,qBAAqBlQ;IAE3B,IAAImQ;IACJ,MAAMC,gBAAgBrO;IACtB,IAAIqO,eAAe;QACjB,MAAMC,YAAYrN,QAAQqN,SAAS;QACnC,IAAIC;QACJ,IAAI;YACF,8EAA8E;YAC9E,MAAMC,gBAAgB,MAAMC,MAC1B,CAAC,iBAAiB,EAAEH,UAAU,UAAU,CAAC,EACzCI,IAAI,CAAC,CAACC,MAAQA,IAAIC,IAAI;YACxBL,YAAYC,aAAa,CAAC,EAAE;QAC9B,EAAE,OAAM,CAAC;QACT,IAAID,WAAW;YACbH,sBAAsBG,UAAUH,mBAAmB;QACrD;IACF;IAEA,MAAMS,cAA0C;QAC9CC,kBAAkBtM;QAClBuM,sBAAsBlM;QACtBmM,aAAa;QACbC,iBAAiB;QACjB,MAAMC,KAAIC,GAAG,EAAER,GAAG,EAAES,UAAU;gBAExBD;YADJ,+DAA+D;YAC/D,KAAIA,WAAAA,IAAInF,GAAG,qBAAPmF,SAAS/F,UAAU,CAAC,gCAAgC;gBACtD,MAAMiG,SAASnR,2BAA2BiR,IAAInF,GAAG;gBAEjD,IAAIqF,QAAQ;oBACV,MAAMC,kBAAkB,CAAC,CAAC,EAAED,OAAO9G,IAAI,CACpCtG,GAAG,CAAC,CAACsN,QAAkBC,mBAAmBD,QAC1C/R,IAAI,CAAC,MAAM;oBAEd,MAAMiS,uBAAuB7Q,oBAAoB0Q;oBAEjD,MAAMT,YACHa,UAAU,CAAC;wBACVpI,MAAMmI;wBACNE,YAAY;wBACZC,YAAY/M;wBACZmH,KAAKmF,IAAInF,GAAG;oBACd,GACC6F,KAAK,CAACC,QAAQ3I,KAAK;gBACxB;YACF;YAEA,KAAK,MAAMC,cAAc6G,YAAa;gBACpC,IAAI8B,aAAa;gBAEjB,MAAM3I,WAAW+H,KAAKR,KAAK;oBACzBoB,aAAa;gBACf;gBAEA,IAAI,CAACA,YAAY;oBACf,OAAO;wBAAEC,UAAU;oBAAK;gBAC1B;YACF;YAEA,4BAA4B;YAC5B,OAAO;gBAAEA,UAAUnN;YAAU;QAC/B;QAEA,2EAA2E;QAC3EoN,OAAMd,GAAG,EAAEe,MAAc,EAAEC,IAAI,EAAEC,SAAS;YACxCvP,SAASwP,aAAa,CAAClB,KAAKe,QAAQC,MAAM,CAACzF;gBACzC0F,UAAU1F;gBACV,MAAMS,eAA+B,IAAIrE;gBACzC,MAAMiG,gBAAiD,IAAIjG;gBAE3DwD,QAAQL,GAAG,CAACS;gBACZH,aAAa7B,GAAG,CAACgC,QAAQ;oBACvBS;oBACAC,aAAa,IAAItE;oBACjBwE,kBAAkB,EAAE;oBACpByB;gBACF;gBAEArC,OAAO4F,EAAE,CAAC,SAAS;oBACjB,8BAA8B;oBAC9B,KAAK,MAAM1D,gBAAgBG,cAAchC,MAAM,GAAI;wBACjD6B,aAAaC,MAAM,oBAAnBD,aAAaC,MAAM,MAAnBD;oBACF;oBACArC,aAAaJ,MAAM,CAACO;oBACpBJ,QAAQH,MAAM,CAACO;gBACjB;gBAEAA,OAAO6F,gBAAgB,CAAC,WAAW,CAAC,EAAE9E,IAAI,EAAE;oBAC1C,MAAM+E,aAAazN,KAAKC,KAAK,CAC3B,OAAOyI,SAAS,WAAWA,KAAKnJ,QAAQ,KAAKmJ;oBAG/C,mBAAmB;oBACnB,OAAQ+E,WAAWC,KAAK;wBACtB,KAAK;4BAAY;gCACfnM,gBAAgBoM,gBAAgB,CAC9BF,WAAWG,QAAQ,EACnBxR,OAAOqR,WAAWI,SAAS,GAC3BzR,OAAOqR,WAAWK,OAAO,GACzBL,WAAWM,UAAU;gCAEvB;4BACF;wBACA,KAAK;4BACHxM,gBAAgBoM,gBAAgB,CAC9BF,WAAWC,KAAK,EAChBtR,OAAOqR,WAAWI,SAAS,GAC3BzR,OAAOqR,WAAWK,OAAO,GACzB;gCACEE,gBAAgBP,WAAWO,cAAc;gCACzCzJ,MAAMkJ,WAAWlJ,IAAI;gCACrB0J,cAAcR,WAAWQ,YAAY;4BACvC;4BAEF;wBACF,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;wBACL,KAAK;4BACH,MAAM,EAAEC,eAAe,EAAEC,eAAe,EAAE,GAAGV;4BAC7C,IAAIS,iBAAiB;gCACnBjT,IAAImT,IAAI,CAACvR;4BACX;4BACA,IACEwR,MAAMC,OAAO,CAACH,oBACd,OAAOA,eAAe,CAAC,EAAE,KAAK,UAC9B;gCACA,MAAMI,oBAAoBJ,eAAe,CAAC,EAAE,CACzC7O,OAAO,CAAC,gBAAgB,KACxBA,OAAO,CAAC,mBAAmB;gCAC9BrE,IAAImT,IAAI,CACN,CAAC,+CAA+C,EAAEG,kBAAkB,yEAAyE,CAAC;4BAElJ;4BACA;wBACF,KAAK;4BAEH;wBAEF;4BACE,kCAAkC;4BAClC,IAAI,CAACd,WAAWtD,IAAI,EAAE;gCACpB,MAAM,qBAA+C,CAA/C,IAAIqE,MAAM,CAAC,0BAA0B,EAAE9F,KAAK,CAAC,CAAC,GAA9C,qBAAA;2CAAA;gDAAA;kDAAA;gCAA8C;4BACtD;oBACJ;oBAEA,qBAAqB;oBACrB,OAAQ+E,WAAWtD,IAAI;wBACrB,KAAK;4BACHJ,qBAAqBpC,QAAQ8F,WAAWjI,IAAI;4BAC5C;wBAEF,KAAK;4BACH+E,yBAAyB5C,QAAQ8F,WAAWjI,IAAI;4BAChD;wBAEF;4BACE,IAAI,CAACiI,WAAWC,KAAK,EAAE;gCACrB,MAAM,qBAAyD,CAAzD,IAAIc,MAAM,CAAC,oCAAoC,EAAE9F,KAAK,CAAC,CAAC,GAAxD,qBAAA;2CAAA;gDAAA;kDAAA;gCAAwD;4BAChE;oBACJ;gBACF;gBAEA,MAAM+F,qBAA+C;oBACnDjG,QAAQzN,4BAA4B2T,mBAAmB;oBACvDhG,MAAM;wBAAEnK;oBAAU;gBACpB;gBACAmJ,aAAaC,QAAQ8G;gBAErB,MAAME,SAA6B,EAAE;gBAErC,KAAK,MAAMC,eAAenK,mBAAmBuD,MAAM,GAAI;oBACrD,KAAK,MAAM6G,SAASD,YAAY5G,MAAM,GAAI;wBACxC,IAAI6G,MAAM3G,QAAQ,KAAK,WAAW;4BAChCyG,OAAO3F,IAAI,CAAC;gCACV8F,SAASzR,YAAYwR;4BACvB;wBACF,OAAO;4BACLvS,mBAAmBuS;wBACrB;oBACF;gBACF;gBAEA,IAAIjR,wBAAwBmR,aAAa,GAAGC,KAAKC,GAAG,IAAI;oBACtDrR,wBAAwBmR,aAAa,GAAG;gBAC1C;;gBAEE,CAAA;oBACA,MAAMG,cAAc,MAAM9D;oBAE1B,MAAM+D,OAAmB;wBACvB3G,QAAQzN,4BAA4BqU,IAAI;wBACxCT;wBACAU,UAAU,EAAE;wBACZC,MAAM;wBACNJ;wBACAK,OAAO;4BACLlE;wBACF;wBACAmE,cAAc5R;oBAChB;oBAEA8J,aAAaC,QAAQwH;gBACvB,CAAA;YACF;QACF;QAEAvH,MAAKY,MAAM;YACT,MAAMzI,UAAUC,KAAK6H,SAAS,CAACW;YAC/B,KAAK,MAAMb,UAAUJ,QAAS;gBAC5BI,OAAOC,IAAI,CAAC7H;YACd;QACF;QAEA0P,mBAAkBC,MAAM;QACtB,uBAAuB;QACzB;QACAC;QACE,uBAAuB;QACzB;QACA,MAAMC,UAAS;QACf,MAAMC,sBAAqBtL,IAAI;YAC7B,MAAMuL,cAAcnT,YAAY,OAAO,UAAU4H;YACjD,MAAMwL,gBAAgBpT,YAAY,SAAS,UAAU4H;YAErD,MAAMyL,iBAAiBxL,sBAAsBwD,MAAM;YAEnD,MAAMiI,kBACJxL,mBAAmBuB,GAAG,CAAC8J,gBACvBrL,mBAAmBuB,GAAG,CAAC+J;YAEzB,IAAIE,oBAAoBnQ,aAAamQ,gBAAgBpJ,IAAI,GAAG,GAAG;gBAC7D,+FAA+F;gBAC/F,OAAO;uBAAImJ;uBAAmBC,gBAAgBjI,MAAM;iBAAG,CACpD9I,GAAG,CAAC,CAAC2P;oBACJ,MAAMqB,iBAAiB7S,YAAYwR;oBACnC,IAAIA,MAAM3G,QAAQ,KAAK,WAAW;wBAChC5L,mBAAmBuS;wBACnB,OAAO;oBACT,OAAO,IAAIrR,iBAAiBqR,QAAQ;wBAClC5T,IAAImJ,KAAK,CAAC8L;oBACZ;oBAEA,OAAO,qBAAyB,CAAzB,IAAI1B,MAAM0B,iBAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAwB;gBACjC,GACCjI,MAAM,CAAC,CAAC7D,QAAUA,UAAU;YACjC;YAEA,4CAA4C;YAC5C,MAAMuK,SAAS,EAAE;YACjB,KAAK,MAAME,SAASmB,eAAgB;gBAClC,IAAInB,MAAM3G,QAAQ,KAAK,WAAW;oBAChCyG,OAAO3F,IAAI,CAAC,qBAA6B,CAA7B,IAAIwF,MAAMnR,YAAYwR,SAAtB,qBAAA;+BAAA;oCAAA;sCAAA;oBAA4B;gBAC1C;YACF;YACA,KAAK,MAAMD,eAAenK,mBAAmBuD,MAAM,GAAI;gBACrD,KAAK,MAAM6G,SAASD,YAAY5G,MAAM,GAAI;oBACxC,IAAI6G,MAAM3G,QAAQ,KAAK,WAAW;wBAChC,MAAM4G,UAAUzR,YAAYwR;wBAC5BF,OAAO3F,IAAI,CAAC,qBAAkB,CAAlB,IAAIwF,MAAMM,UAAV,qBAAA;mCAAA;wCAAA;0CAAA;wBAAiB;oBAC/B,OAAO;wBACLxS,mBAAmBuS;oBACrB;gBACF;YACF;YACA,OAAOF;QACT;QACA,MAAMwB,YAAW,EACf,yCAAyC;QACzCC,uBAAuB,EACxB;YACC,IAAIA,yBAAyB;gBAC3B,KAAK,MAAM,CAAC/K,KAAKgL,WAAW,IAAIvM,0BAA2B;oBACzDsB,kBAAkBC,KAAKgL,YAAY;wBAAE9K,OAAO;oBAAK;gBACnD;gBAEA,MAAM5J;gBACN,IAAI,CAACiM,IAAI,CAAC;oBACRY,QAAQzN,4BAA4BuV,wBAAwB;oBAC5DhB,MAAM5F,OAAO,EAAEpC;gBACjB;YACF;QACF;QACA,MAAMiJ;QACJ,uBAAuB;QACzB;QACA,MAAM5D,YAAW,EACfpI,MAAMiM,SAAS,EACf,oBAAoB;QACpB,cAAc;QACdC,QAAQ,EACR5D,UAAU,EACV6D,KAAK,EACLzJ,KAAKP,UAAU,EAChB;YACC,yFAAyF;YACzF,6FAA6F;YAC7F,IAAI,CAACmG,YAAY;gBACf,IAAI2D,cAAc,eAAe;gBACjC,IAAIA,cAAc,mBAAmB;gBACrC,IAAIA,cAAc,oBAAoB;gBACtC,IAAIA,cAAc,wBAAwB;YAC5C;YAEA,OAAOjP,gBACJoP,UAAU,CAAC,eAAe;gBACzBH;YACF,GACCI,YAAY,CAAC;gBACZ,IAAIxV,cAAcyV,QAAQ,CAACL,cAAcA,cAAc,WAAW;oBAChE;gBACF;gBAEA,MAAMxL;gBAEN,qGAAqG;gBACrG,IAAI8L,WAIFjE,cACC,MAAMnQ,iBACLiE,aACA6P,WACAjQ,WAAWwQ,cAAc,EACzB5Q,KAAK6Q,QAAQ,EACb7Q,KAAK8Q,MAAM;gBAGf,yEAAyE;gBACzE,oEAAoE;gBACpE,IAAI,CAACR,YAAY5D,cAAc9P,yBAAyB8P,aAAa;oBACnE4D,WAAW5D,WAAW4D,QAAQ;gBAChC;gBAEA,IAAIlM,OAAOuM,SAASvM,IAAI;gBACxB,IAAIkM,UAAU;oBACZ,MAAMS,iBAAiBlU,iBAAiBuH;oBAExC,8DAA8D;oBAC9D,MAAM4M,mBAAmBV,SAASxI,MAAM,CACtC,CAACzC,OAASxI,iBAAiBwI,UAAU0L;oBAGvC,4EAA4E;oBAC5E3M,OAAO4M,gBAAgB,CAACA,iBAAiB9R,MAAM,GAAG,EAAE;gBACtD;gBAEA,MAAM+R,WAAWvE,CAAAA,8BAAAA,WAAYuE,QAAQ,KAAIZ;gBAEzC,IAAIjM,SAAS,WAAW;oBACtB,IAAI4C,iBAAiBX,cAAc4K,UAAU1K,YAAY;oBACzD,IAAI;wBACF,MAAMzK,sBAAsB;4BAC1BwI;4BACAgG,aAAazG;4BACbU;4BACAgG,aAAavK,KAAKe,SAAS,CAACC,QAAQ;4BACpCwJ,oBAAoB7K;4BACpB8K,WAAW;4BACXC,OAAO;gCACL5B;gCACA6B,uBAAuB,CAACrE,IAAIsE,QAAQC;oCAClClH,0BAA0B6B,GAAG,CAACc,IAAIsE;oCAClC5F,YAAYkM,cAAc,CAAC5K,IAAIsE,OAAOuG,WAAW;oCACjD,OAAOlM,kBAAkBqB,IAAIsE,QAAQ;wCACnCxF,OAAOyF;oCACT;gCACF;4BACF;wBACF;oBACF,SAAU;wBACR7D;oBACF;oBACA;gBACF;gBAEA,MAAMoK,iBAAiBT,SAASU,UAAU,CAACnL,UAAU,CAAC;gBACtD,MAAMoL,2BAA2BvU,oBAC/B4T,SAASY,QAAQ,CAACpS,OAAO,CAACa,KAAK8Q,MAAM,IAAI,IAAI,KAC7C1Q,WAAWwQ,cAAc,EACzB;gBAEF,MAAMY,oBAAoBF,2BACtBlV,wCACEgI,MACA7J,QAAQoW,SAASY,QAAQ,KAE3BnN;gBAEJ,MAAMqN,QAAQL,iBACVvN,mBAAmBE,GAAG,CAAC8B,GAAG,CAAC2L,qBAC3B3N,mBAAmBO,IAAI,CAACyB,GAAG,CAACzB;gBAEhC,IAAI,CAACqN,OAAO;oBACV,gDAAgD;oBAChD,IAAIrN,SAAS,eAAe;oBAC5B,IAAIA,SAAS,mBAAmB;oBAChC,IAAIA,SAAS,oBAAoB;oBACjC,IAAIA,SAAS,wBAAwB;oBAErC,MAAM,IAAIhJ,kBAAkB,CAAC,gBAAgB,EAAEgJ,MAAM;gBACvD;gBAEA,2DAA2D;gBAC3D,4CAA4C;gBAC5C,mCAAmC;gBACnC,IAAImM,SAASkB,MAAMzH,IAAI,KAAK,QAAQ;oBAClC,MAAM,qBAA8D,CAA9D,IAAIqE,MAAM,CAAC,0CAA0C,EAAEjK,MAAM,GAA7D,qBAAA;+BAAA;oCAAA;sCAAA;oBAA6D;gBACrE;gBAEA,MAAM4C,iBAAiBX,cAAc4K,UAAU1K,YAAY;gBAC3D,IAAI;oBACF,MAAMxK,gBAAgB;wBACpBsE;wBACA+D;wBACA6M;wBACAQ;wBACAnN;wBACAgG,aAAazG;wBACbU;wBACAG;wBACA6F,aAAavK,KAAKe,SAAS,CAACC,QAAQ;wBACpCwJ,oBAAoB7K;wBACpB8K,WAAW;wBAEXC,OAAO;4BACL5B;4BACA6B,uBAAuB,CAACrE,IAAIsE,QAAQC;gCAClClH,0BAA0B6B,GAAG,CAACc,IAAIsE;gCAClC5F,YAAYkM,cAAc,CAAC5K,IAAIsE,OAAOuG,WAAW;gCACjD,OAAOlM,kBAAkBqB,IAAIsE,QAAQ;oCACnCxF,OAAOyF;gCACT;4BACF;wBACF;oBACF;gBACF,SAAU;oBACR7D;gBACF;YACF;QACJ;QACAmD;YACE,KAAK,MAAMuH,YAAYtK,QAAS;gBAC9B,0EAA0E;gBAC1EsK,SAASC,SAAS;YACpB;YACAvK,QAAQe,KAAK;QACf;IACF;IAEAkC,gCAAgCsC,KAAK,CAAC,CAACjN;QACrCkN,QAAQ3I,KAAK,CAACvE;QACd3B,QAAQ6T,IAAI,CAAC;IACf;IAEA,wBAAwB;IACxB,MAAM/M;IACN,MAAMN,eAAesN,cAAc,CAAC;QAClCtH,aAAavK,KAAKe,SAAS,CAACC,QAAQ;QACpCwJ,oBAAoB7K;QACpB2K,aAAazG;IACf;IAEA,eAAeiO;QACb,WAAW,MAAMC,iBAAiBzS,QAAQ0S,mBAAmB,CAAC,IAAK;YACjE,OAAQD,cAAcE,UAAU;gBAC9B,KAAK;oBAAS;wBACZtG,YAAYlE,IAAI,CAAC;4BAAEY,QAAQzN,4BAA4BsX,QAAQ;wBAAC;wBAChE;oBACF;gBACA,KAAK;oBAAO;wBACVvK;wBAEA,SAASwK,UACPC,SAAwC,EACxCxJ,MAAsB;4BAEtB,KAAK,MAAMhB,YAAYgB,OAAOf,MAAM,GAAI;gCACtC,KAAK,MAAM,CAAC3C,KAAKwJ,MAAM,IAAI9G,SAAU;oCACnC,IAAI8G,MAAM3G,QAAQ,KAAK,WAAW;oCAClC,IAAIqK,UAAU3L,GAAG,CAACvB,MAAM;oCAExB,MAAMyJ,UAAUzR,YAAYwR;oCAE5B0D,UAAU5M,GAAG,CAACN,KAAK;wCACjByJ;wCACA0D,SAAS3D,MAAM4D,MAAM,GACjB/U,8BAA8BmR,MAAM4D,MAAM,IAC1C3S;oCACN;gCACF;4BACF;wBACF;wBAEA,MAAM6O,SAAS,IAAI5K;wBACnBuO,UAAU3D,QAAQlK;wBAElB,KAAK,MAAMkD,UAAUJ,QAAS;4BAC5B,MAAMY,QAAQX,aAAaxB,GAAG,CAAC2B;4BAC/B,IAAI,CAACQ,OAAO;gCACV;4BACF;4BAEA,MAAMuK,eAAe,IAAI3O,IAAI4K;4BAC7B2D,UAAUI,cAAcvK,MAAMC,YAAY;4BAE1CV,aAAaC,QAAQ;gCACnBa,QAAQzN,4BAA4B4X,KAAK;gCACzCrD,MAAM5F,OAAO,EAAEpC;gCACfqH,QAAQ;uCAAI+D,aAAa1K,MAAM;iCAAG;gCAClCqH,UAAU,EAAE;4BACd;wBACF;wBAEA,IAAIhI,kBAAkB;4BACpB,MAAMuL,OAAOV,cAAcW,KAAK,CAACC,QAAQ;4BACzC,MAAMC,cACJH,OAAO,OAAO,GAAGpU,KAAKwU,KAAK,CAACJ,OAAO,OAAO,GAAG,CAAC,CAAC,GAAG,GAAGA,KAAK,EAAE,CAAC;4BAC/D3X,IAAIyS,KAAK,CAAC,CAAC,YAAY,EAAEqF,aAAa;4BACtC1L,mBAAmB;wBACrB;wBACA;oBACF;gBACA;YACF;QACF;IACF;IAEA4K,uBAAuBnF,KAAK,CAAC,CAACjN;QAC5BkN,QAAQ3I,KAAK,CAACvE;QACd3B,QAAQ6T,IAAI,CAAC;IACf;IAEA,OAAOjG;AACT"}