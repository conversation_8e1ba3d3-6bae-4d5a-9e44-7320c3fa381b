'use client';

import { useState, useEffect } from 'react';
import { registerServiceWorker } from '@/utils/serviceWorker';
import LandingNavbar from '@/components/landing/LandingNavbar';
import HeroSection from '@/components/landing/HeroSection';
import TrustBadges from '@/components/landing/TrustBadges';
import FeaturesSection from '@/components/landing/FeaturesSection';
import RoutingVisualization from '@/components/landing/RoutingVisualization';
import TestimonialsSection from '@/components/landing/TestimonialsSection';
import CTASection from '@/components/landing/CTASection';
import Footer from '@/components/landing/Footer';
import EnhancedGridBackground from '@/components/landing/EnhancedGridBackground';
import { useInstantNavigation } from '@/hooks/useInstantNavigation';

export default function LandingPage() {
  const [isLoaded, setIsLoaded] = useState(false);

  // Initialize instant navigation for all pages
  useInstantNavigation();

  useEffect(() => {
    // Register service worker for caching
    registerServiceWorker({
      onSuccess: () => console.log('✅ Service Worker registered for caching'),
      onUpdate: () => console.log('🔄 New content available'),
      onError: (error) => console.warn('⚠️ Service Worker registration failed:', error)
    });

    setIsLoaded(true);
  }, []);

  // Prefetch likely next pages on hover
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const prefetchPages = () => {
        // Prefetch auth pages since users likely to sign up
        const link1 = document.createElement('link');
        link1.rel = 'prefetch';
        link1.href = '/pricing';
        document.head.appendChild(link1);

        const link2 = document.createElement('link');
        link2.rel = 'prefetch';
        link2.href = '/auth/signup';
        document.head.appendChild(link2);

        const link3 = document.createElement('link');
        link3.rel = 'prefetch';
        link3.href = '/features';
        document.head.appendChild(link3);
      };

      // Prefetch after initial load
      const prefetchTimer = setTimeout(prefetchPages, 1000);
      return () => clearTimeout(prefetchTimer);
    }
  }, []);

  if (!isLoaded) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-white">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#ff6b35] mx-auto mb-2"></div>
          <p className="text-black text-sm">Loading RouKey...</p>
        </div>
      </div>
    );
  }

  // Debug: Add a simple test to see if CSS is working
  return (
    <div className="min-h-screen bg-red-500">
      <div className="bg-blue-500 text-white p-4">
        <h1 className="text-2xl font-bold">CSS Test</h1>
        <p>If you see this styled, CSS is working</p>
      </div>
      <div className="bg-green-500 text-white p-4">
        <p>This should be green background</p>
      </div>
    </div>
  );

  return (
    <div className="min-h-screen bg-white relative overflow-hidden">
      {/* Main Grid Background - Reduced Opacity */}
      <EnhancedGridBackground
        gridSize={60}
        opacity={0.032}
        color="#000000"
        variant="subtle"
        animated={true}
        className="fixed inset-0"
      />

      {/* Tech Grid Overlay for Hero Section */}
      <div className="absolute inset-0 z-0">
        <div className="h-screen relative">
          <EnhancedGridBackground
            gridSize={45}
            opacity={0.024}
            color="#ff6b35"
            variant="tech"
            animated={true}
            glowEffect={true}
            className="absolute inset-0"
          />
        </div>
      </div>

      {/* Premium Grid for Features Section */}
      <div className="absolute inset-0 z-0" style={{ top: '200vh' }}>
        <div className="h-screen relative">
          <EnhancedGridBackground
            gridSize={35}
            opacity={0.056}
            color="#000000"
            variant="premium"
            animated={true}
            className="absolute inset-0"
          />
        </div>
      </div>

      {/* Content */}
      <div className="relative z-10">
        <LandingNavbar />

        <main>
          <HeroSection />
          <TrustBadges />
          <RoutingVisualization />
          <FeaturesSection />
          <TestimonialsSection />
          <CTASection />
        </main>

        <Footer />
      </div>
    </div>
  );
}
