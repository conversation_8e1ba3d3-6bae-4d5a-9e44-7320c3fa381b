{"version": 3, "sources": ["../../src/lib/mkcert.ts"], "sourcesContent": ["import fs from 'node:fs'\nimport path from 'node:path'\nimport { X509Certificate, createPrivate<PERSON><PERSON> } from 'node:crypto'\nimport { getCacheDirectory } from './helpers/get-cache-directory'\nimport * as Log from '../build/output/log'\nimport { execSync } from 'node:child_process'\nconst { WritableStream } = require('node:stream/web') as {\n  WritableStream: typeof global.WritableStream\n}\n\nconst MKCERT_VERSION = 'v1.4.4'\n\nexport interface SelfSignedCertificate {\n  key: string\n  cert: string\n  rootCA?: string\n}\n\nfunction getBinaryName() {\n  const platform = process.platform\n  const arch = process.arch === 'x64' ? 'amd64' : process.arch\n\n  if (platform === 'win32') {\n    return `mkcert-${MKCERT_VERSION}-windows-${arch}.exe`\n  }\n  if (platform === 'darwin') {\n    return `mkcert-${MKCERT_VERSION}-darwin-${arch}`\n  }\n  if (platform === 'linux') {\n    return `mkcert-${MKCERT_VERSION}-linux-${arch}`\n  }\n\n  throw new Error(`Unsupported platform: ${platform}`)\n}\n\nasync function downloadBinary() {\n  try {\n    const binaryName = getBinaryName()\n    const cacheDirectory = getCacheDirectory('mkcert')\n    const binaryPath = path.join(cacheDirectory, binaryName)\n\n    if (fs.existsSync(binaryPath)) {\n      return binaryPath\n    }\n\n    const downloadUrl = `https://github.com/FiloSottile/mkcert/releases/download/${MKCERT_VERSION}/${binaryName}`\n\n    await fs.promises.mkdir(cacheDirectory, { recursive: true })\n\n    Log.info(`Downloading mkcert package...`)\n\n    const response = await fetch(downloadUrl)\n\n    if (!response.ok || !response.body) {\n      throw new Error(`request failed with status ${response.status}`)\n    }\n\n    Log.info(`Download response was successful, writing to disk`)\n\n    const binaryWriteStream = fs.createWriteStream(binaryPath)\n\n    await response.body.pipeTo(\n      new WritableStream({\n        write(chunk) {\n          return new Promise((resolve, reject) => {\n            binaryWriteStream.write(chunk, (error) => {\n              if (error) {\n                reject(error)\n                return\n              }\n\n              resolve()\n            })\n          })\n        },\n        close() {\n          return new Promise((resolve, reject) => {\n            binaryWriteStream.close((error) => {\n              if (error) {\n                reject(error)\n                return\n              }\n\n              resolve()\n            })\n          })\n        },\n      })\n    )\n\n    await fs.promises.chmod(binaryPath, 0o755)\n\n    return binaryPath\n  } catch (err) {\n    Log.error('Error downloading mkcert:', err)\n  }\n}\n\nexport async function createSelfSignedCertificate(\n  host?: string,\n  certDir: string = 'certificates'\n): Promise<SelfSignedCertificate | undefined> {\n  try {\n    const binaryPath = await downloadBinary()\n    if (!binaryPath) throw new Error('missing mkcert binary')\n\n    const resolvedCertDir = path.resolve(process.cwd(), `./${certDir}`)\n\n    await fs.promises.mkdir(resolvedCertDir, {\n      recursive: true,\n    })\n\n    const keyPath = path.resolve(resolvedCertDir, 'localhost-key.pem')\n    const certPath = path.resolve(resolvedCertDir, 'localhost.pem')\n\n    if (fs.existsSync(keyPath) && fs.existsSync(certPath)) {\n      const cert = new X509Certificate(fs.readFileSync(certPath))\n      const key = fs.readFileSync(keyPath)\n\n      if (\n        cert.checkHost(host ?? 'localhost') &&\n        cert.checkPrivateKey(createPrivateKey(key))\n      ) {\n        Log.info('Using already generated self signed certificate')\n        const caLocation = execSync(`\"${binaryPath}\" -CAROOT`).toString().trim()\n\n        return {\n          key: keyPath,\n          cert: certPath,\n          rootCA: `${caLocation}/rootCA.pem`,\n        }\n      }\n    }\n\n    Log.info(\n      'Attempting to generate self signed certificate. This may prompt for your password'\n    )\n\n    const defaultHosts = ['localhost', '127.0.0.1', '::1']\n\n    const hosts =\n      host && !defaultHosts.includes(host)\n        ? [...defaultHosts, host]\n        : defaultHosts\n\n    execSync(\n      `\"${binaryPath}\" -install -key-file \"${keyPath}\" -cert-file \"${certPath}\" ${hosts.join(\n        ' '\n      )}`,\n      { stdio: 'ignore' }\n    )\n\n    const caLocation = execSync(`\"${binaryPath}\" -CAROOT`).toString().trim()\n\n    if (!fs.existsSync(keyPath) || !fs.existsSync(certPath)) {\n      throw new Error('Certificate files not found')\n    }\n\n    Log.info(`CA Root certificate created in ${caLocation}`)\n    Log.info(`Certificates created in ${resolvedCertDir}`)\n\n    const gitignorePath = path.resolve(process.cwd(), './.gitignore')\n\n    if (fs.existsSync(gitignorePath)) {\n      const gitignore = await fs.promises.readFile(gitignorePath, 'utf8')\n      if (!gitignore.includes(certDir)) {\n        Log.info('Adding certificates to .gitignore')\n\n        await fs.promises.appendFile(gitignorePath, `\\n${certDir}`)\n      }\n    }\n\n    return {\n      key: keyPath,\n      cert: certPath,\n      rootCA: `${caLocation}/rootCA.pem`,\n    }\n  } catch (err) {\n    Log.error(\n      'Failed to generate self-signed certificate. Falling back to http.',\n      err\n    )\n  }\n}\n"], "names": ["fs", "path", "X509Certificate", "createPrivateKey", "getCacheDirectory", "Log", "execSync", "WritableStream", "require", "MKCERT_VERSION", "getBinaryName", "platform", "process", "arch", "Error", "downloadBinary", "binaryName", "cacheDirectory", "binaryPath", "join", "existsSync", "downloadUrl", "promises", "mkdir", "recursive", "info", "response", "fetch", "ok", "body", "status", "binaryWriteStream", "createWriteStream", "pipeTo", "write", "chunk", "Promise", "resolve", "reject", "error", "close", "chmod", "err", "createSelfSignedCertificate", "host", "certDir", "resolvedCertDir", "cwd", "keyP<PERSON>", "certPath", "cert", "readFileSync", "key", "checkHost", "checkPrivateKey", "caLocation", "toString", "trim", "rootCA", "defaultHosts", "hosts", "includes", "stdio", "gitignore<PERSON>ath", "gitignore", "readFile", "appendFile"], "mappings": "AAAA,OAAOA,QAAQ,UAAS;AACxB,OAAOC,UAAU,YAAW;AAC5B,SAASC,eAAe,EAAEC,gBAAgB,QAAQ,cAAa;AAC/D,SAASC,iBAAiB,QAAQ,gCAA+B;AACjE,YAAYC,SAAS,sBAAqB;AAC1C,SAASC,QAAQ,QAAQ,qBAAoB;AAC7C,MAAM,EAAEC,cAAc,EAAE,GAAGC,QAAQ;AAInC,MAAMC,iBAAiB;AAQvB,SAASC;IACP,MAAMC,WAAWC,QAAQD,QAAQ;IACjC,MAAME,OAAOD,QAAQC,IAAI,KAAK,QAAQ,UAAUD,QAAQC,IAAI;IAE5D,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,SAAS,EAAEI,KAAK,IAAI,CAAC;IACvD;IACA,IAAIF,aAAa,UAAU;QACzB,OAAO,CAAC,OAAO,EAAEF,eAAe,QAAQ,EAAEI,MAAM;IAClD;IACA,IAAIF,aAAa,SAAS;QACxB,OAAO,CAAC,OAAO,EAAEF,eAAe,OAAO,EAAEI,MAAM;IACjD;IAEA,MAAM,qBAA8C,CAA9C,IAAIC,MAAM,CAAC,sBAAsB,EAAEH,UAAU,GAA7C,qBAAA;eAAA;oBAAA;sBAAA;IAA6C;AACrD;AAEA,eAAeI;IACb,IAAI;QACF,MAAMC,aAAaN;QACnB,MAAMO,iBAAiBb,kBAAkB;QACzC,MAAMc,aAAajB,KAAKkB,IAAI,CAACF,gBAAgBD;QAE7C,IAAIhB,GAAGoB,UAAU,CAACF,aAAa;YAC7B,OAAOA;QACT;QAEA,MAAMG,cAAc,CAAC,wDAAwD,EAAEZ,eAAe,CAAC,EAAEO,YAAY;QAE7G,MAAMhB,GAAGsB,QAAQ,CAACC,KAAK,CAACN,gBAAgB;YAAEO,WAAW;QAAK;QAE1DnB,IAAIoB,IAAI,CAAC,CAAC,6BAA6B,CAAC;QAExC,MAAMC,WAAW,MAAMC,MAAMN;QAE7B,IAAI,CAACK,SAASE,EAAE,IAAI,CAACF,SAASG,IAAI,EAAE;YAClC,MAAM,qBAA0D,CAA1D,IAAIf,MAAM,CAAC,2BAA2B,EAAEY,SAASI,MAAM,EAAE,GAAzD,qBAAA;uBAAA;4BAAA;8BAAA;YAAyD;QACjE;QAEAzB,IAAIoB,IAAI,CAAC,CAAC,iDAAiD,CAAC;QAE5D,MAAMM,oBAAoB/B,GAAGgC,iBAAiB,CAACd;QAE/C,MAAMQ,SAASG,IAAI,CAACI,MAAM,CACxB,IAAI1B,eAAe;YACjB2B,OAAMC,KAAK;gBACT,OAAO,IAAIC,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBG,KAAK,CAACC,OAAO,CAACI;wBAC9B,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;YACAG;gBACE,OAAO,IAAIJ,QAAQ,CAACC,SAASC;oBAC3BP,kBAAkBS,KAAK,CAAC,CAACD;wBACvB,IAAIA,OAAO;4BACTD,OAAOC;4BACP;wBACF;wBAEAF;oBACF;gBACF;YACF;QACF;QAGF,MAAMrC,GAAGsB,QAAQ,CAACmB,KAAK,CAACvB,YAAY;QAEpC,OAAOA;IACT,EAAE,OAAOwB,KAAK;QACZrC,IAAIkC,KAAK,CAAC,6BAA6BG;IACzC;AACF;AAEA,OAAO,eAAeC,4BACpBC,IAAa,EACbC,UAAkB,cAAc;IAEhC,IAAI;QACF,MAAM3B,aAAa,MAAMH;QACzB,IAAI,CAACG,YAAY,MAAM,qBAAkC,CAAlC,IAAIJ,MAAM,0BAAV,qBAAA;mBAAA;wBAAA;0BAAA;QAAiC;QAExD,MAAMgC,kBAAkB7C,KAAKoC,OAAO,CAACzB,QAAQmC,GAAG,IAAI,CAAC,EAAE,EAAEF,SAAS;QAElE,MAAM7C,GAAGsB,QAAQ,CAACC,KAAK,CAACuB,iBAAiB;YACvCtB,WAAW;QACb;QAEA,MAAMwB,UAAU/C,KAAKoC,OAAO,CAACS,iBAAiB;QAC9C,MAAMG,WAAWhD,KAAKoC,OAAO,CAACS,iBAAiB;QAE/C,IAAI9C,GAAGoB,UAAU,CAAC4B,YAAYhD,GAAGoB,UAAU,CAAC6B,WAAW;YACrD,MAAMC,OAAO,IAAIhD,gBAAgBF,GAAGmD,YAAY,CAACF;YACjD,MAAMG,MAAMpD,GAAGmD,YAAY,CAACH;YAE5B,IACEE,KAAKG,SAAS,CAACT,QAAQ,gBACvBM,KAAKI,eAAe,CAACnD,iBAAiBiD,OACtC;gBACA/C,IAAIoB,IAAI,CAAC;gBACT,MAAM8B,aAAajD,SAAS,CAAC,CAAC,EAAEY,WAAW,SAAS,CAAC,EAAEsC,QAAQ,GAAGC,IAAI;gBAEtE,OAAO;oBACLL,KAAKJ;oBACLE,MAAMD;oBACNS,QAAQ,GAAGH,WAAW,WAAW,CAAC;gBACpC;YACF;QACF;QAEAlD,IAAIoB,IAAI,CACN;QAGF,MAAMkC,eAAe;YAAC;YAAa;YAAa;SAAM;QAEtD,MAAMC,QACJhB,QAAQ,CAACe,aAAaE,QAAQ,CAACjB,QAC3B;eAAIe;YAAcf;SAAK,GACvBe;QAENrD,SACE,CAAC,CAAC,EAAEY,WAAW,sBAAsB,EAAE8B,QAAQ,cAAc,EAAEC,SAAS,EAAE,EAAEW,MAAMzC,IAAI,CACpF,MACC,EACH;YAAE2C,OAAO;QAAS;QAGpB,MAAMP,aAAajD,SAAS,CAAC,CAAC,EAAEY,WAAW,SAAS,CAAC,EAAEsC,QAAQ,GAAGC,IAAI;QAEtE,IAAI,CAACzD,GAAGoB,UAAU,CAAC4B,YAAY,CAAChD,GAAGoB,UAAU,CAAC6B,WAAW;YACvD,MAAM,qBAAwC,CAAxC,IAAInC,MAAM,gCAAV,qBAAA;uBAAA;4BAAA;8BAAA;YAAuC;QAC/C;QAEAT,IAAIoB,IAAI,CAAC,CAAC,+BAA+B,EAAE8B,YAAY;QACvDlD,IAAIoB,IAAI,CAAC,CAAC,wBAAwB,EAAEqB,iBAAiB;QAErD,MAAMiB,gBAAgB9D,KAAKoC,OAAO,CAACzB,QAAQmC,GAAG,IAAI;QAElD,IAAI/C,GAAGoB,UAAU,CAAC2C,gBAAgB;YAChC,MAAMC,YAAY,MAAMhE,GAAGsB,QAAQ,CAAC2C,QAAQ,CAACF,eAAe;YAC5D,IAAI,CAACC,UAAUH,QAAQ,CAAChB,UAAU;gBAChCxC,IAAIoB,IAAI,CAAC;gBAET,MAAMzB,GAAGsB,QAAQ,CAAC4C,UAAU,CAACH,eAAe,CAAC,EAAE,EAAElB,SAAS;YAC5D;QACF;QAEA,OAAO;YACLO,KAAKJ;YACLE,MAAMD;YACNS,QAAQ,GAAGH,WAAW,WAAW,CAAC;QACpC;IACF,EAAE,OAAOb,KAAK;QACZrC,IAAIkC,KAAK,CACP,qEACAG;IAEJ;AACF"}