{"version": 3, "sources": ["../../../../../src/client/components/react-dev-overlay/pages/hot-reloader-client.ts"], "sourcesContent": ["// TODO: Remove use of `any` type. Fix no-use-before-define violations.\n/* eslint-disable @typescript-eslint/no-use-before-define */\n/**\n * MIT License\n *\n * Copyright (c) 2013-present, Facebook, Inc.\n *\n * Permission is hereby granted, free of charge, to any person obtaining a copy\n * of this software and associated documentation files (the \"Software\"), to deal\n * in the Software without restriction, including without limitation the rights\n * to use, copy, modify, merge, publish, distribute, sublicense, and/or sell\n * copies of the Software, and to permit persons to whom the Software is\n * furnished to do so, subject to the following conditions:\n *\n * The above copyright notice and this permission notice shall be included in all\n * copies or substantial portions of the Software.\n *\n * THE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\n * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\n * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\n * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\n * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\n * OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\n * SOFTWARE.\n */\n\n/// <reference types=\"webpack/module.d.ts\" />\n\n// This file is a modified version of the Create React App HMR dev client that\n// can be found here:\n// https://github.com/facebook/create-react-app/blob/v3.4.1/packages/react-dev-utils/webpackHotDevClient.js\n\n/// <reference types=\"webpack/module.d.ts\" />\n\nimport {\n  register,\n  onBuildError,\n  onBuildOk,\n  onBeforeRefresh,\n  onRefresh,\n  onVersionInfo,\n  onStaticIndicator,\n  onDevIndicator,\n} from './client'\nimport stripAnsi from 'next/dist/compiled/strip-ansi'\nimport { addMessageListener, sendMessage } from './websocket'\nimport formatWebpackMessages from '../utils/format-webpack-messages'\nimport { HMR_ACTIONS_SENT_TO_BROWSER } from '../../../../server/dev/hot-reloader-types'\nimport type {\n  HMR_ACTION_TYPES,\n  TurbopackMsgToBrowser,\n} from '../../../../server/dev/hot-reloader-types'\nimport {\n  REACT_REFRESH_FULL_RELOAD,\n  REACT_REFRESH_FULL_RELOAD_FROM_ERROR,\n  reportInvalidHmrMessage,\n} from '../shared'\nimport { RuntimeErrorHandler } from '../../errors/runtime-error-handler'\nimport reportHmrLatency from '../utils/report-hmr-latency'\nimport { TurbopackHmr } from '../utils/turbopack-hot-reloader-common'\n\n// This alternative WebpackDevServer combines the functionality of:\n// https://github.com/webpack/webpack-dev-server/blob/webpack-1/client/index.js\n// https://github.com/webpack/webpack/blob/webpack-1/hot/dev-server.js\n\n// It only supports their simplest configuration (hot updates on same server).\n// It makes some opinionated choices on top, like adding a syntax error overlay\n// that looks similar to our console output. The error overlay is inspired by:\n// https://github.com/glenjamin/webpack-hot-middleware\n\ndeclare global {\n  interface Window {\n    __nextDevClientId: number\n  }\n}\n\nwindow.__nextDevClientId = Math.round(Math.random() * 100 + Date.now())\n\nlet customHmrEventHandler: any\nlet turbopackMessageListeners: ((msg: TurbopackMsgToBrowser) => void)[] = []\nexport default function connect() {\n  register()\n\n  addMessageListener((payload) => {\n    if (!('action' in payload)) {\n      return\n    }\n\n    try {\n      processMessage(payload)\n    } catch (err: unknown) {\n      reportInvalidHmrMessage(payload, err)\n    }\n  })\n\n  return {\n    subscribeToHmrEvent(handler: any) {\n      customHmrEventHandler = handler\n    },\n    onUnrecoverableError() {\n      RuntimeErrorHandler.hadRuntimeError = true\n    },\n    addTurbopackMessageListener(cb: (msg: TurbopackMsgToBrowser) => void) {\n      turbopackMessageListeners.push(cb)\n    },\n    sendTurbopackMessage(msg: string) {\n      sendMessage(msg)\n    },\n    handleUpdateError(err: unknown) {\n      performFullReload(err)\n    },\n  }\n}\n\n// Remember some state related to hot module replacement.\nvar isFirstCompilation = true\nvar mostRecentCompilationHash: string | null = null\nvar hasCompileErrors = false\n\nfunction clearOutdatedErrors() {\n  // Clean up outdated compile errors, if any.\n  if (typeof console !== 'undefined' && typeof console.clear === 'function') {\n    if (hasCompileErrors) {\n      console.clear()\n    }\n  }\n}\n\n// Successful compilation.\nfunction handleSuccess() {\n  clearOutdatedErrors()\n  hasCompileErrors = false\n\n  if (process.env.TURBOPACK) {\n    const hmrUpdate = turbopackHmr!.onBuilt()\n    if (hmrUpdate != null) {\n      reportHmrLatency(\n        sendMessage,\n        [...hmrUpdate.updatedModules],\n        hmrUpdate.startMsSinceEpoch,\n        hmrUpdate.endMsSinceEpoch,\n        hmrUpdate.hasUpdates\n      )\n    }\n    onBuildOk()\n  } else {\n    const isHotUpdate =\n      !isFirstCompilation ||\n      (window.__NEXT_DATA__.page !== '/_error' && isUpdateAvailable())\n\n    // Attempt to apply hot updates or reload.\n    if (isHotUpdate) {\n      tryApplyUpdatesWebpack()\n    }\n  }\n\n  isFirstCompilation = false\n}\n\n// Compilation with warnings (e.g. ESLint).\nfunction handleWarnings(warnings: any) {\n  clearOutdatedErrors()\n\n  const isHotUpdate = !isFirstCompilation\n  isFirstCompilation = false\n  hasCompileErrors = false\n\n  function printWarnings() {\n    // Print warnings to the console.\n    const formatted = formatWebpackMessages({\n      warnings: warnings,\n      errors: [],\n    })\n\n    if (typeof console !== 'undefined' && typeof console.warn === 'function') {\n      for (let i = 0; i < formatted.warnings?.length; i++) {\n        if (i === 5) {\n          console.warn(\n            'There were more warnings in other files.\\n' +\n              'You can find a complete log in the terminal.'\n          )\n          break\n        }\n        console.warn(stripAnsi(formatted.warnings[i]))\n      }\n    }\n  }\n\n  printWarnings()\n\n  // Attempt to apply hot updates or reload.\n  if (isHotUpdate) {\n    tryApplyUpdatesWebpack()\n  }\n}\n\n// Compilation with errors (e.g. syntax error or missing modules).\nfunction handleErrors(errors: any) {\n  clearOutdatedErrors()\n\n  isFirstCompilation = false\n  hasCompileErrors = true\n\n  // \"Massage\" webpack messages.\n  var formatted = formatWebpackMessages({\n    errors: errors,\n    warnings: [],\n  })\n\n  // Only show the first error.\n\n  onBuildError(formatted.errors[0])\n\n  // Also log them to the console.\n  if (typeof console !== 'undefined' && typeof console.error === 'function') {\n    for (var i = 0; i < formatted.errors.length; i++) {\n      console.error(stripAnsi(formatted.errors[i]))\n    }\n  }\n\n  // Do not attempt to reload now.\n  // We will reload on next success instead.\n  if (process.env.__NEXT_TEST_MODE) {\n    if (self.__NEXT_HMR_CB) {\n      self.__NEXT_HMR_CB(formatted.errors[0])\n      self.__NEXT_HMR_CB = null\n    }\n  }\n}\n\nlet webpackStartMsSinceEpoch: number | null = null\nconst turbopackHmr: TurbopackHmr | null = process.env.TURBOPACK\n  ? new TurbopackHmr()\n  : null\nlet isrManifest: Record<string, boolean> = {}\n\n// There is a newer version of the code available.\nfunction handleAvailableHash(hash: string) {\n  // Update last known compilation hash.\n  mostRecentCompilationHash = hash\n}\n\nexport function handleStaticIndicator() {\n  if (process.env.__NEXT_DEV_INDICATOR) {\n    const routeInfo = window.next.router.components[window.next.router.pathname]\n    const pageComponent = routeInfo?.Component\n    const appComponent = window.next.router.components['/_app']?.Component\n    const isDynamicPage =\n      Boolean(pageComponent?.getInitialProps) || Boolean(routeInfo?.__N_SSP)\n    const hasAppGetInitialProps =\n      Boolean(appComponent?.getInitialProps) &&\n      appComponent?.getInitialProps !== appComponent?.origGetInitialProps\n\n    const isPageStatic =\n      window.location.pathname in isrManifest ||\n      (!isDynamicPage && !hasAppGetInitialProps)\n\n    onStaticIndicator(isPageStatic)\n  }\n}\n\n/** Handles messages from the server for the Pages Router. */\nfunction processMessage(obj: HMR_ACTION_TYPES) {\n  if (!('action' in obj)) {\n    return\n  }\n\n  switch (obj.action) {\n    case HMR_ACTIONS_SENT_TO_BROWSER.ISR_MANIFEST: {\n      isrManifest = obj.data\n      handleStaticIndicator()\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILDING: {\n      if (process.env.TURBOPACK) {\n        turbopackHmr!.onBuilding()\n      } else {\n        webpackStartMsSinceEpoch = Date.now()\n        console.log('[Fast Refresh] rebuilding')\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.BUILT:\n    case HMR_ACTIONS_SENT_TO_BROWSER.SYNC: {\n      if (obj.hash) handleAvailableHash(obj.hash)\n\n      const { errors, warnings } = obj\n\n      // Is undefined when it's a 'built' event\n      if ('versionInfo' in obj) onVersionInfo(obj.versionInfo)\n      if ('devIndicator' in obj) onDevIndicator(obj.devIndicator)\n\n      const hasErrors = Boolean(errors && errors.length)\n      if (hasErrors) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-error',\n            errorCount: errors.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleErrors(errors)\n      }\n\n      // NOTE: Turbopack does not currently send warnings\n      const hasWarnings = Boolean(warnings && warnings.length)\n      if (hasWarnings) {\n        sendMessage(\n          JSON.stringify({\n            event: 'client-warning',\n            warningCount: warnings.length,\n            clientId: window.__nextDevClientId,\n          })\n        )\n        return handleWarnings(warnings)\n      }\n\n      sendMessage(\n        JSON.stringify({\n          event: 'client-success',\n          clientId: window.__nextDevClientId,\n        })\n      )\n      return handleSuccess()\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_COMPONENT_CHANGES: {\n      turbopackHmr?.onServerComponentChanges()\n      if (hasCompileErrors || RuntimeErrorHandler.hadRuntimeError) {\n        window.location.reload()\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.SERVER_ERROR: {\n      const { errorJSON } = obj\n      if (errorJSON) {\n        const { message, stack } = JSON.parse(errorJSON)\n        const error = new Error(message)\n        error.stack = stack\n        handleErrors([error])\n      }\n      return\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED: {\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_CONNECTED,\n          data: obj.data,\n        })\n      }\n      break\n    }\n    case HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE: {\n      turbopackHmr!.onTurbopackMessage(obj)\n      onBeforeRefresh()\n      for (const listener of turbopackMessageListeners) {\n        listener({\n          type: HMR_ACTIONS_SENT_TO_BROWSER.TURBOPACK_MESSAGE,\n          data: obj.data,\n        })\n      }\n      if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n        performFullReload(null)\n      }\n      onRefresh()\n      break\n    }\n    default: {\n      if (customHmrEventHandler) {\n        customHmrEventHandler(obj)\n        break\n      }\n      break\n    }\n  }\n}\n\n// Is there a newer version of this code available?\nfunction isUpdateAvailable() {\n  /* globals __webpack_hash__ */\n  // __webpack_hash__ is the hash of the current compilation.\n  // It's a global variable injected by Webpack.\n  return mostRecentCompilationHash !== __webpack_hash__\n}\n\n// Webpack disallows updates in other states.\nfunction canApplyUpdates() {\n  return module.hot.status() === 'idle'\n}\nfunction afterApplyUpdates(fn: () => void) {\n  if (canApplyUpdates()) {\n    fn()\n  } else {\n    function handler(status: string) {\n      if (status === 'idle') {\n        module.hot.removeStatusHandler(handler)\n        fn()\n      }\n    }\n    module.hot.addStatusHandler(handler)\n  }\n}\n\n// Attempt to update code on the fly, fall back to a hard reload.\nfunction tryApplyUpdatesWebpack() {\n  if (!module.hot) {\n    // HotModuleReplacementPlugin is not in Webpack configuration.\n    console.error('HotModuleReplacementPlugin is not in Webpack configuration.')\n    // window.location.reload();\n    return\n  }\n\n  if (!isUpdateAvailable() || !canApplyUpdates()) {\n    onBuildOk()\n    return\n  }\n\n  function handleApplyUpdates(\n    err: any,\n    updatedModules: (string | number)[] | null\n  ) {\n    if (err || RuntimeErrorHandler.hadRuntimeError || updatedModules == null) {\n      if (err) {\n        console.warn(REACT_REFRESH_FULL_RELOAD)\n      } else if (RuntimeErrorHandler.hadRuntimeError) {\n        console.warn(REACT_REFRESH_FULL_RELOAD_FROM_ERROR)\n      }\n      performFullReload(err)\n      return\n    }\n\n    onBuildOk()\n\n    if (isUpdateAvailable()) {\n      // While we were updating, there was a new update! Do it again.\n      tryApplyUpdatesWebpack()\n      return\n    }\n\n    onRefresh()\n    reportHmrLatency(\n      sendMessage,\n      updatedModules,\n      webpackStartMsSinceEpoch!,\n      Date.now()\n    )\n\n    if (process.env.__NEXT_TEST_MODE) {\n      afterApplyUpdates(() => {\n        if (self.__NEXT_HMR_CB) {\n          self.__NEXT_HMR_CB()\n          self.__NEXT_HMR_CB = null\n        }\n      })\n    }\n  }\n\n  // https://webpack.js.org/api/hot-module-replacement/#check\n  module.hot\n    .check(/* autoApply */ false)\n    .then((updatedModules: (string | number)[] | null) => {\n      if (updatedModules == null) {\n        return null\n      }\n\n      // We should always handle an update, even if updatedModules is empty (but\n      // non-null) for any reason. That's what webpack would normally do:\n      // https://github.com/webpack/webpack/blob/3aa6b6bc3a64/lib/hmr/HotModuleReplacement.runtime.js#L296-L298\n      onBeforeRefresh()\n      // https://webpack.js.org/api/hot-module-replacement/#apply\n      return module.hot.apply()\n    })\n    .then(\n      (updatedModules: (string | number)[] | null) => {\n        handleApplyUpdates(null, updatedModules)\n      },\n      (err: any) => {\n        handleApplyUpdates(err, null)\n      }\n    )\n}\n\nexport function performFullReload(err: any) {\n  const stackTrace =\n    err &&\n    ((err.stack && err.stack.split('\\n').slice(0, 5).join('\\n')) ||\n      err.message ||\n      err + '')\n\n  sendMessage(\n    JSON.stringify({\n      event: 'client-full-reload',\n      stackTrace,\n      hadRuntimeError: !!RuntimeErrorHandler.hadRuntimeError,\n      dependencyChain: err ? err.dependencyChain : undefined,\n    })\n  )\n\n  window.location.reload()\n}\n"], "names": ["register", "onBuildError", "onBuildOk", "onBeforeRefresh", "onRefresh", "onVersionInfo", "onStaticIndicator", "onDevIndicator", "stripAnsi", "addMessageListener", "sendMessage", "formatWebpackMessages", "HMR_ACTIONS_SENT_TO_BROWSER", "REACT_REFRESH_FULL_RELOAD", "REACT_REFRESH_FULL_RELOAD_FROM_ERROR", "reportInvalidHmrMessage", "RuntimeError<PERSON>andler", "reportHmrLatency", "TurbopackHmr", "window", "__nextDevClientId", "Math", "round", "random", "Date", "now", "customHmrEventHandler", "turbopackMessageListeners", "connect", "payload", "processMessage", "err", "subscribeToHmrEvent", "handler", "onUnrecoverableError", "hadRuntimeError", "addTurbopackMessageListener", "cb", "push", "sendTurbopackMessage", "msg", "handleUpdateError", "performFullReload", "isFirstCompilation", "mostRecentCompilationHash", "hasCompileErrors", "clearOutdatedErrors", "console", "clear", "handleSuccess", "process", "env", "TURBOPACK", "hmrUpdate", "turbopackHmr", "onBuilt", "updatedModules", "startMsSinceEpoch", "endMsSinceEpoch", "hasUpdates", "isHotUpdate", "__NEXT_DATA__", "page", "isUpdateAvailable", "tryApplyUpdatesWebpack", "handleWarnings", "warnings", "printWarnings", "formatted", "errors", "warn", "i", "length", "handleErrors", "error", "__NEXT_TEST_MODE", "self", "__NEXT_HMR_CB", "webpackStartMsSinceEpoch", "isrManifest", "handleAvailableHash", "hash", "handleStaticIndicator", "__NEXT_DEV_INDICATOR", "routeInfo", "next", "router", "components", "pathname", "pageComponent", "Component", "appComponent", "isDynamicPage", "Boolean", "getInitialProps", "__N_SSP", "hasAppGetInitialProps", "origGetInitialProps", "isPageStatic", "location", "obj", "action", "ISR_MANIFEST", "data", "BUILDING", "onBuilding", "log", "BUILT", "SYNC", "versionInfo", "devIndicator", "hasErrors", "JSON", "stringify", "event", "errorCount", "clientId", "hasWarnings", "warningCount", "SERVER_COMPONENT_CHANGES", "onServerComponentChanges", "reload", "SERVER_ERROR", "errorJSON", "message", "stack", "parse", "Error", "TURBOPACK_CONNECTED", "listener", "type", "TURBOPACK_MESSAGE", "onTurbopackMessage", "__webpack_hash__", "canApplyUpdates", "module", "hot", "status", "afterApplyUpdates", "fn", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "addStatusHandler", "handleApplyUpdates", "check", "then", "apply", "stackTrace", "split", "slice", "join", "dependency<PERSON><PERSON>n", "undefined"], "mappings": "AAAA,uEAAuE;AACvE,0DAA0D,GAC1D;;;;;;;;;;;;;;;;;;;;;;CAsBC,GAED,6CAA6C;AAE7C,8EAA8E;AAC9E,qBAAqB;AACrB,2GAA2G;AAE3G,6CAA6C;AAE7C,SACEA,QAAQ,EACRC,YAAY,EACZC,SAAS,EACTC,eAAe,EACfC,SAAS,EACTC,aAAa,EACbC,iBAAiB,EACjBC,cAAc,QACT,WAAU;AACjB,OAAOC,eAAe,gCAA+B;AACrD,SAASC,kBAAkB,EAAEC,WAAW,QAAQ,cAAa;AAC7D,OAAOC,2BAA2B,mCAAkC;AACpE,SAASC,2BAA2B,QAAQ,4CAA2C;AAKvF,SACEC,yBAAyB,EACzBC,oCAAoC,EACpCC,uBAAuB,QAClB,YAAW;AAClB,SAASC,mBAAmB,QAAQ,qCAAoC;AACxE,OAAOC,sBAAsB,8BAA6B;AAC1D,SAASC,YAAY,QAAQ,yCAAwC;AAiBrEC,OAAOC,iBAAiB,GAAGC,KAAKC,KAAK,CAACD,KAAKE,MAAM,KAAK,MAAMC,KAAKC,GAAG;AAEpE,IAAIC;AACJ,IAAIC,4BAAsE,EAAE;AAC5E,eAAe,SAASC;IACtB5B;IAEAS,mBAAmB,CAACoB;QAClB,IAAI,CAAE,CAAA,YAAYA,OAAM,GAAI;YAC1B;QACF;QAEA,IAAI;YACFC,eAAeD;QACjB,EAAE,OAAOE,KAAc;YACrBhB,wBAAwBc,SAASE;QACnC;IACF;IAEA,OAAO;QACLC,qBAAoBC,OAAY;YAC9BP,wBAAwBO;QAC1B;QACAC;YACElB,oBAAoBmB,eAAe,GAAG;QACxC;QACAC,6BAA4BC,EAAwC;YAClEV,0BAA0BW,IAAI,CAACD;QACjC;QACAE,sBAAqBC,GAAW;YAC9B9B,YAAY8B;QACd;QACAC,mBAAkBV,GAAY;YAC5BW,kBAAkBX;QACpB;IACF;AACF;AAEA,yDAAyD;AACzD,IAAIY,qBAAqB;AACzB,IAAIC,4BAA2C;AAC/C,IAAIC,mBAAmB;AAEvB,SAASC;IACP,4CAA4C;IAC5C,IAAI,OAAOC,YAAY,eAAe,OAAOA,QAAQC,KAAK,KAAK,YAAY;QACzE,IAAIH,kBAAkB;YACpBE,QAAQC,KAAK;QACf;IACF;AACF;AAEA,0BAA0B;AAC1B,SAASC;IACPH;IACAD,mBAAmB;IAEnB,IAAIK,QAAQC,GAAG,CAACC,SAAS,EAAE;QACzB,MAAMC,YAAYC,aAAcC,OAAO;QACvC,IAAIF,aAAa,MAAM;YACrBpC,iBACEP,aACA;mBAAI2C,UAAUG,cAAc;aAAC,EAC7BH,UAAUI,iBAAiB,EAC3BJ,UAAUK,eAAe,EACzBL,UAAUM,UAAU;QAExB;QACAzD;IACF,OAAO;QACL,MAAM0D,cACJ,CAACjB,sBACAxB,OAAO0C,aAAa,CAACC,IAAI,KAAK,aAAaC;QAE9C,0CAA0C;QAC1C,IAAIH,aAAa;YACfI;QACF;IACF;IAEArB,qBAAqB;AACvB;AAEA,2CAA2C;AAC3C,SAASsB,eAAeC,QAAa;IACnCpB;IAEA,MAAMc,cAAc,CAACjB;IACrBA,qBAAqB;IACrBE,mBAAmB;IAEnB,SAASsB;QACP,iCAAiC;QACjC,MAAMC,YAAYzD,sBAAsB;YACtCuD,UAAUA;YACVG,QAAQ,EAAE;QACZ;QAEA,IAAI,OAAOtB,YAAY,eAAe,OAAOA,QAAQuB,IAAI,KAAK,YAAY;gBACpDF;YAApB,IAAK,IAAIG,IAAI,GAAGA,MAAIH,sBAAAA,UAAUF,QAAQ,qBAAlBE,oBAAoBI,MAAM,GAAED,IAAK;gBACnD,IAAIA,MAAM,GAAG;oBACXxB,QAAQuB,IAAI,CACV,+CACE;oBAEJ;gBACF;gBACAvB,QAAQuB,IAAI,CAAC9D,UAAU4D,UAAUF,QAAQ,CAACK,EAAE;YAC9C;QACF;IACF;IAEAJ;IAEA,0CAA0C;IAC1C,IAAIP,aAAa;QACfI;IACF;AACF;AAEA,kEAAkE;AAClE,SAASS,aAAaJ,MAAW;IAC/BvB;IAEAH,qBAAqB;IACrBE,mBAAmB;IAEnB,8BAA8B;IAC9B,IAAIuB,YAAYzD,sBAAsB;QACpC0D,QAAQA;QACRH,UAAU,EAAE;IACd;IAEA,6BAA6B;IAE7BjE,aAAamE,UAAUC,MAAM,CAAC,EAAE;IAEhC,gCAAgC;IAChC,IAAI,OAAOtB,YAAY,eAAe,OAAOA,QAAQ2B,KAAK,KAAK,YAAY;QACzE,IAAK,IAAIH,IAAI,GAAGA,IAAIH,UAAUC,MAAM,CAACG,MAAM,EAAED,IAAK;YAChDxB,QAAQ2B,KAAK,CAAClE,UAAU4D,UAAUC,MAAM,CAACE,EAAE;QAC7C;IACF;IAEA,gCAAgC;IAChC,0CAA0C;IAC1C,IAAIrB,QAAQC,GAAG,CAACwB,gBAAgB,EAAE;QAChC,IAAIC,KAAKC,aAAa,EAAE;YACtBD,KAAKC,aAAa,CAACT,UAAUC,MAAM,CAAC,EAAE;YACtCO,KAAKC,aAAa,GAAG;QACvB;IACF;AACF;AAEA,IAAIC,2BAA0C;AAC9C,MAAMxB,eAAoCJ,QAAQC,GAAG,CAACC,SAAS,GAC3D,IAAIlC,iBACJ;AACJ,IAAI6D,cAAuC,CAAC;AAE5C,kDAAkD;AAClD,SAASC,oBAAoBC,IAAY;IACvC,sCAAsC;IACtCrC,4BAA4BqC;AAC9B;AAEA,OAAO,SAASC;IACd,IAAIhC,QAAQC,GAAG,CAACgC,oBAAoB,EAAE;YAGfhE;QAFrB,MAAMiE,YAAYjE,OAAOkE,IAAI,CAACC,MAAM,CAACC,UAAU,CAACpE,OAAOkE,IAAI,CAACC,MAAM,CAACE,QAAQ,CAAC;QAC5E,MAAMC,gBAAgBL,6BAAAA,UAAWM,SAAS;QAC1C,MAAMC,gBAAexE,sCAAAA,OAAOkE,IAAI,CAACC,MAAM,CAACC,UAAU,CAAC,QAAQ,qBAAtCpE,oCAAwCuE,SAAS;QACtE,MAAME,gBACJC,QAAQJ,iCAAAA,cAAeK,eAAe,KAAKD,QAAQT,6BAAAA,UAAWW,OAAO;QACvE,MAAMC,wBACJH,QAAQF,gCAAAA,aAAcG,eAAe,KACrCH,CAAAA,gCAAAA,aAAcG,eAAe,OAAKH,gCAAAA,aAAcM,mBAAmB;QAErE,MAAMC,eACJ/E,OAAOgF,QAAQ,CAACX,QAAQ,IAAIT,eAC3B,CAACa,iBAAiB,CAACI;QAEtB1F,kBAAkB4F;IACpB;AACF;AAEA,2DAA2D,GAC3D,SAASpE,eAAesE,GAAqB;IAC3C,IAAI,CAAE,CAAA,YAAYA,GAAE,GAAI;QACtB;IACF;IAEA,OAAQA,IAAIC,MAAM;QAChB,KAAKzF,4BAA4B0F,YAAY;YAAE;gBAC7CvB,cAAcqB,IAAIG,IAAI;gBACtBrB;gBACA;YACF;QACA,KAAKtE,4BAA4B4F,QAAQ;YAAE;gBACzC,IAAItD,QAAQC,GAAG,CAACC,SAAS,EAAE;oBACzBE,aAAcmD,UAAU;gBAC1B,OAAO;oBACL3B,2BAA2BtD,KAAKC,GAAG;oBACnCsB,QAAQ2D,GAAG,CAAC;gBACd;gBACA;YACF;QACA,KAAK9F,4BAA4B+F,KAAK;QACtC,KAAK/F,4BAA4BgG,IAAI;YAAE;gBACrC,IAAIR,IAAInB,IAAI,EAAED,oBAAoBoB,IAAInB,IAAI;gBAE1C,MAAM,EAAEZ,MAAM,EAAEH,QAAQ,EAAE,GAAGkC;gBAE7B,yCAAyC;gBACzC,IAAI,iBAAiBA,KAAK/F,cAAc+F,IAAIS,WAAW;gBACvD,IAAI,kBAAkBT,KAAK7F,eAAe6F,IAAIU,YAAY;gBAE1D,MAAMC,YAAYlB,QAAQxB,UAAUA,OAAOG,MAAM;gBACjD,IAAIuC,WAAW;oBACbrG,YACEsG,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPC,YAAY9C,OAAOG,MAAM;wBACzB4C,UAAUjG,OAAOC,iBAAiB;oBACpC;oBAEF,OAAOqD,aAAaJ;gBACtB;gBAEA,mDAAmD;gBACnD,MAAMgD,cAAcxB,QAAQ3B,YAAYA,SAASM,MAAM;gBACvD,IAAI6C,aAAa;oBACf3G,YACEsG,KAAKC,SAAS,CAAC;wBACbC,OAAO;wBACPI,cAAcpD,SAASM,MAAM;wBAC7B4C,UAAUjG,OAAOC,iBAAiB;oBACpC;oBAEF,OAAO6C,eAAeC;gBACxB;gBAEAxD,YACEsG,KAAKC,SAAS,CAAC;oBACbC,OAAO;oBACPE,UAAUjG,OAAOC,iBAAiB;gBACpC;gBAEF,OAAO6B;YACT;QACA,KAAKrC,4BAA4B2G,wBAAwB;YAAE;gBACzDjE,gCAAAA,aAAckE,wBAAwB;gBACtC,IAAI3E,oBAAoB7B,oBAAoBmB,eAAe,EAAE;oBAC3DhB,OAAOgF,QAAQ,CAACsB,MAAM;gBACxB;gBACA;YACF;QACA,KAAK7G,4BAA4B8G,YAAY;YAAE;gBAC7C,MAAM,EAAEC,SAAS,EAAE,GAAGvB;gBACtB,IAAIuB,WAAW;oBACb,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAE,GAAGb,KAAKc,KAAK,CAACH;oBACtC,MAAMjD,QAAQ,qBAAkB,CAAlB,IAAIqD,MAAMH,UAAV,qBAAA;+BAAA;oCAAA;sCAAA;oBAAiB;oBAC/BlD,MAAMmD,KAAK,GAAGA;oBACdpD,aAAa;wBAACC;qBAAM;gBACtB;gBACA;YACF;QACA,KAAK9D,4BAA4BoH,mBAAmB;YAAE;gBACpD,KAAK,MAAMC,YAAYtG,0BAA2B;oBAChDsG,SAAS;wBACPC,MAAMtH,4BAA4BoH,mBAAmB;wBACrDzB,MAAMH,IAAIG,IAAI;oBAChB;gBACF;gBACA;YACF;QACA,KAAK3F,4BAA4BuH,iBAAiB;YAAE;gBAClD7E,aAAc8E,kBAAkB,CAAChC;gBACjCjG;gBACA,KAAK,MAAM8H,YAAYtG,0BAA2B;oBAChDsG,SAAS;wBACPC,MAAMtH,4BAA4BuH,iBAAiB;wBACnD5B,MAAMH,IAAIG,IAAI;oBAChB;gBACF;gBACA,IAAIvF,oBAAoBmB,eAAe,EAAE;oBACvCY,QAAQuB,IAAI,CAACxD;oBACb4B,kBAAkB;gBACpB;gBACAtC;gBACA;YACF;QACA;YAAS;gBACP,IAAIsB,uBAAuB;oBACzBA,sBAAsB0E;oBACtB;gBACF;gBACA;YACF;IACF;AACF;AAEA,mDAAmD;AACnD,SAASrC;IACP,4BAA4B,GAC5B,2DAA2D;IAC3D,8CAA8C;IAC9C,OAAOnB,8BAA8ByF;AACvC;AAEA,6CAA6C;AAC7C,SAASC;IACP,OAAOC,OAAOC,GAAG,CAACC,MAAM,OAAO;AACjC;AACA,SAASC,kBAAkBC,EAAc;IACvC,IAAIL,mBAAmB;QACrBK;IACF,OAAO;QACL,SAAS1G,QAAQwG,MAAc;YAC7B,IAAIA,WAAW,QAAQ;gBACrBF,OAAOC,GAAG,CAACI,mBAAmB,CAAC3G;gBAC/B0G;YACF;QACF;QACAJ,OAAOC,GAAG,CAACK,gBAAgB,CAAC5G;IAC9B;AACF;AAEA,iEAAiE;AACjE,SAAS+B;IACP,IAAI,CAACuE,OAAOC,GAAG,EAAE;QACf,8DAA8D;QAC9DzF,QAAQ2B,KAAK,CAAC;QACd,4BAA4B;QAC5B;IACF;IAEA,IAAI,CAACX,uBAAuB,CAACuE,mBAAmB;QAC9CpI;QACA;IACF;IAEA,SAAS4I,mBACP/G,GAAQ,EACRyB,cAA0C;QAE1C,IAAIzB,OAAOf,oBAAoBmB,eAAe,IAAIqB,kBAAkB,MAAM;YACxE,IAAIzB,KAAK;gBACPgB,QAAQuB,IAAI,CAACzD;YACf,OAAO,IAAIG,oBAAoBmB,eAAe,EAAE;gBAC9CY,QAAQuB,IAAI,CAACxD;YACf;YACA4B,kBAAkBX;YAClB;QACF;QAEA7B;QAEA,IAAI6D,qBAAqB;YACvB,+DAA+D;YAC/DC;YACA;QACF;QAEA5D;QACAa,iBACEP,aACA8C,gBACAsB,0BACAtD,KAAKC,GAAG;QAGV,IAAIyB,QAAQC,GAAG,CAACwB,gBAAgB,EAAE;YAChC+D,kBAAkB;gBAChB,IAAI9D,KAAKC,aAAa,EAAE;oBACtBD,KAAKC,aAAa;oBAClBD,KAAKC,aAAa,GAAG;gBACvB;YACF;QACF;IACF;IAEA,2DAA2D;IAC3D0D,OAAOC,GAAG,CACPO,KAAK,CAAC,aAAa,GAAG,OACtBC,IAAI,CAAC,CAACxF;QACL,IAAIA,kBAAkB,MAAM;YAC1B,OAAO;QACT;QAEA,0EAA0E;QAC1E,mEAAmE;QACnE,yGAAyG;QACzGrD;QACA,2DAA2D;QAC3D,OAAOoI,OAAOC,GAAG,CAACS,KAAK;IACzB,GACCD,IAAI,CACH,CAACxF;QACCsF,mBAAmB,MAAMtF;IAC3B,GACA,CAACzB;QACC+G,mBAAmB/G,KAAK;IAC1B;AAEN;AAEA,OAAO,SAASW,kBAAkBX,GAAQ;IACxC,MAAMmH,aACJnH,OACC,CAAA,AAACA,IAAI8F,KAAK,IAAI9F,IAAI8F,KAAK,CAACsB,KAAK,CAAC,MAAMC,KAAK,CAAC,GAAG,GAAGC,IAAI,CAAC,SACpDtH,IAAI6F,OAAO,IACX7F,MAAM,EAAC;IAEXrB,YACEsG,KAAKC,SAAS,CAAC;QACbC,OAAO;QACPgC;QACA/G,iBAAiB,CAAC,CAACnB,oBAAoBmB,eAAe;QACtDmH,iBAAiBvH,MAAMA,IAAIuH,eAAe,GAAGC;IAC/C;IAGFpI,OAAOgF,QAAQ,CAACsB,MAAM;AACxB"}