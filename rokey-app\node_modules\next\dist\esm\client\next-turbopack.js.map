{"version": 3, "sources": ["../../src/client/next-turbopack.ts"], "sourcesContent": ["// A client-side entry point for Turbopack builds. Includes logic to load chunks,\n// but does not include development-time features like hot module reloading.\n\nimport '../lib/require-instrumentation-client'\n\n// TODO: Remove use of `any` type.\nimport { initialize, version, router, emitter, hydrate } from './'\n// TODO: This seems necessary, but is a module in the `dev` directory.\nimport { displayContent } from './dev/fouc'\n\nwindow.next = {\n  version: `${version}-turbo`,\n  // router is initialized later so it has to be live-binded\n  get router() {\n    return router\n  },\n  emitter,\n}\n;(self as any).__next_set_public_path__ = () => {}\n;(self as any).__webpack_hash__ = ''\n\n// for the page loader\ndeclare let __turbopack_load__: any\n\ninitialize({})\n  .then(() => {\n    // for the page loader\n    ;(self as any).__turbopack_load_page_chunks__ = (\n      page: string,\n      chunksData: any\n    ) => {\n      const chunkPromises = chunksData.map(__turbopack_load__)\n\n      Promise.all(chunkPromises).catch((err) =>\n        console.error('failed to load chunks for page ' + page, err)\n      )\n    }\n\n    return hydrate({ beforeRender: displayContent })\n  })\n  .catch((err) => {\n    console.error('Error was not caught', err)\n  })\n"], "names": ["initialize", "version", "router", "emitter", "hydrate", "displayContent", "window", "next", "self", "__next_set_public_path__", "__webpack_hash__", "then", "__turbopack_load_page_chunks__", "page", "chunksData", "chunkPromises", "map", "__turbopack_load__", "Promise", "all", "catch", "err", "console", "error", "beforeRender"], "mappings": "AAAA,iFAAiF;AACjF,4EAA4E;AAE5E,OAAO,wCAAuC;AAE9C,kCAAkC;AAClC,SAASA,UAAU,EAAEC,OAAO,EAAEC,MAAM,EAAEC,OAAO,EAAEC,OAAO,QAAQ,KAAI;AAClE,sEAAsE;AACtE,SAASC,cAAc,QAAQ,aAAY;AAE3CC,OAAOC,IAAI,GAAG;IACZN,SAAS,AAAC,KAAEA,UAAQ;IACpB,0DAA0D;IAC1D,IAAIC,UAAS;QACX,OAAOA;IACT;IACAC;AACF;AACEK,KAAaC,wBAAwB,GAAG,KAAO;AAC/CD,KAAaE,gBAAgB,GAAG;AAKlCV,WAAW,CAAC,GACTW,IAAI,CAAC;IACJ,sBAAsB;;IACpBH,KAAaI,8BAA8B,GAAG,CAC9CC,MACAC;QAEA,MAAMC,gBAAgBD,WAAWE,GAAG,CAACC;QAErCC,QAAQC,GAAG,CAACJ,eAAeK,KAAK,CAAC,CAACC,MAChCC,QAAQC,KAAK,CAAC,oCAAoCV,MAAMQ;IAE5D;IAEA,OAAOjB,QAAQ;QAAEoB,cAAcnB;IAAe;AAChD,GACCe,KAAK,CAAC,CAACC;IACNC,QAAQC,KAAK,CAAC,wBAAwBF;AACxC"}