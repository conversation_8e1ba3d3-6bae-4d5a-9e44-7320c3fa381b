{"version": 3, "sources": ["../../src/lib/load-custom-routes.ts"], "sourcesContent": ["import type { NextConfig } from '../server/config'\nimport type { Token } from 'next/dist/compiled/path-to-regexp'\n\nimport { bold, yellow } from './picocolors'\nimport { escapeStringRegexp } from '../shared/lib/escape-regexp'\nimport { tryToParsePath } from './try-to-parse-path'\nimport { allowedStatusCodes } from './redirect-status'\nimport { isFullStringUrl } from './url'\n\nexport type RouteHas =\n  | {\n      type: string\n      key: string\n      value?: string\n    }\n  | {\n      type: 'host'\n      key?: undefined\n      value: string\n    }\n\nexport type Rewrite = {\n  source: string\n  destination: string\n  basePath?: false\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n\n  /**\n   * @internal - used internally for routing\n   */\n  internal?: boolean\n}\n\nexport type Header = {\n  source: string\n  basePath?: false\n  locale?: false\n  headers: Array<{ key: string; value: string }>\n  has?: RouteHas[]\n  missing?: RouteHas[]\n\n  /**\n   * @internal - used internally for routing\n   */\n  internal?: boolean\n}\n\n// internal type used for validation (not user facing)\nexport type Redirect = {\n  source: string\n  destination: string\n  basePath?: false\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n\n  /**\n   * @internal - used internally for routing\n   */\n  internal?: boolean\n} & (\n  | {\n      statusCode?: never\n      permanent: boolean\n    }\n  | {\n      statusCode: number\n      permanent?: never\n    }\n)\n\nexport type Middleware = {\n  source: string\n  locale?: false\n  has?: RouteHas[]\n  missing?: RouteHas[]\n}\n\nconst allowedHasTypes = new Set(['header', 'cookie', 'query', 'host'])\nconst namedGroupsRegex = /\\(\\?<([a-zA-Z][a-zA-Z0-9]*)>/g\n\nexport function normalizeRouteRegex(regex: string) {\n  // clean up un-necessary escaping from regex.source which turns / into \\\\/\n  return regex.replace(/\\\\\\//g, '/')\n}\n\nfunction checkRedirect(route: Redirect): {\n  invalidParts: string[]\n  hadInvalidStatus: boolean\n} {\n  const invalidParts: string[] = []\n  let hadInvalidStatus: boolean = false\n\n  if (route.statusCode && !allowedStatusCodes.has(route['statusCode'])) {\n    hadInvalidStatus = true\n    invalidParts.push(`\\`statusCode\\` is not undefined or valid statusCode`)\n  }\n  if (typeof route.permanent !== 'boolean' && !route['statusCode']) {\n    invalidParts.push(`\\`permanent\\` is not set to \\`true\\` or \\`false\\``)\n  }\n\n  return {\n    invalidParts,\n    hadInvalidStatus,\n  }\n}\n\nfunction checkHeader(route: Header): string[] {\n  const invalidParts: string[] = []\n\n  if (!Array.isArray(route.headers)) {\n    invalidParts.push('`headers` field must be an array')\n  } else if (route.headers.length === 0) {\n    invalidParts.push('`headers` field cannot be empty')\n  } else {\n    for (const header of route.headers) {\n      if (!header || typeof header !== 'object') {\n        invalidParts.push(\n          \"`headers` items must be object with { key: '', value: '' }\"\n        )\n        break\n      }\n      if (typeof header.key !== 'string') {\n        invalidParts.push('`key` in header item must be string')\n        break\n      }\n      if (typeof header.value !== 'string') {\n        invalidParts.push('`value` in header item must be string')\n        break\n      }\n    }\n  }\n  return invalidParts\n}\n\nexport type RouteType = 'rewrite' | 'redirect' | 'header'\n\nexport function checkCustomRoutes(\n  routes: Redirect[] | Header[] | Rewrite[] | Middleware[],\n  type: RouteType | 'middleware'\n): void {\n  if (!Array.isArray(routes)) {\n    console.error(\n      `Error: ${type}s must return an array, received ${typeof routes}.\\n` +\n        `See here for more info: https://nextjs.org/docs/messages/routes-must-be-array`\n    )\n    process.exit(1)\n  }\n\n  let numInvalidRoutes = 0\n  let hadInvalidStatus = false\n  let hadInvalidHas = false\n  let hadInvalidMissing = false\n\n  const allowedKeys = new Set<string>(['source', 'locale', 'has', 'missing'])\n\n  if (type === 'rewrite') {\n    allowedKeys.add('basePath')\n    allowedKeys.add('destination')\n  }\n  if (type === 'redirect') {\n    allowedKeys.add('basePath')\n    allowedKeys.add('statusCode')\n    allowedKeys.add('permanent')\n    allowedKeys.add('destination')\n  }\n  if (type === 'header') {\n    allowedKeys.add('basePath')\n    allowedKeys.add('headers')\n  }\n\n  for (const route of routes) {\n    if (!route || typeof route !== 'object') {\n      console.error(\n        `The route ${JSON.stringify(\n          route\n        )} is not a valid object with \\`source\\`${\n          type !== 'middleware'\n            ? ` and \\`${type === 'header' ? 'headers' : 'destination'}\\``\n            : ''\n        }`\n      )\n      numInvalidRoutes++\n      continue\n    }\n\n    if (\n      type === 'rewrite' &&\n      (route as Rewrite).basePath === false &&\n      !(\n        (route as Rewrite).destination.startsWith('http://') ||\n        (route as Rewrite).destination.startsWith('https://')\n      )\n    ) {\n      console.error(\n        `The route ${\n          (route as Rewrite).source\n        } rewrites urls outside of the basePath. Please use a destination that starts with \\`http://\\` or \\`https://\\` https://nextjs.org/docs/messages/invalid-external-rewrite`\n      )\n      numInvalidRoutes++\n      continue\n    }\n\n    const keys = Object.keys(route)\n    const invalidKeys = keys.filter((key) => !allowedKeys.has(key))\n    const invalidParts: string[] = []\n\n    if (\n      'basePath' in route &&\n      typeof route.basePath !== 'undefined' &&\n      route.basePath !== false\n    ) {\n      invalidParts.push('`basePath` must be undefined or false')\n    }\n\n    if (typeof route.locale !== 'undefined' && route.locale !== false) {\n      invalidParts.push('`locale` must be undefined or false')\n    }\n\n    const checkInvalidHasMissing = (\n      items: any,\n      fieldName: 'has' | 'missing'\n    ) => {\n      let hadInvalidItem = false\n\n      if (typeof items !== 'undefined' && !Array.isArray(items)) {\n        invalidParts.push(\n          `\\`${fieldName}\\` must be undefined or valid has object`\n        )\n        hadInvalidItem = true\n      } else if (items) {\n        const invalidHasItems = []\n\n        for (const hasItem of items) {\n          let invalidHasParts = []\n\n          if (!allowedHasTypes.has(hasItem.type)) {\n            invalidHasParts.push(`invalid type \"${hasItem.type}\"`)\n          }\n          if (typeof hasItem.key !== 'string' && hasItem.type !== 'host') {\n            invalidHasParts.push(`invalid key \"${hasItem.key}\"`)\n          }\n          if (\n            typeof hasItem.value !== 'undefined' &&\n            typeof hasItem.value !== 'string'\n          ) {\n            invalidHasParts.push(`invalid value \"${hasItem.value}\"`)\n          }\n          if (typeof hasItem.value === 'undefined' && hasItem.type === 'host') {\n            invalidHasParts.push(`value is required for \"host\" type`)\n          }\n\n          if (invalidHasParts.length > 0) {\n            invalidHasItems.push(\n              `${invalidHasParts.join(', ')} for ${JSON.stringify(hasItem)}`\n            )\n          }\n        }\n\n        if (invalidHasItems.length > 0) {\n          hadInvalidItem = true\n          const itemStr = `item${invalidHasItems.length === 1 ? '' : 's'}`\n\n          console.error(\n            `Invalid \\`${fieldName}\\` ${itemStr}:\\n` +\n              invalidHasItems.join('\\n')\n          )\n          console.error()\n          invalidParts.push(`invalid \\`${fieldName}\\` ${itemStr} found`)\n        }\n      }\n      return hadInvalidItem\n    }\n    if (checkInvalidHasMissing(route.has, 'has')) {\n      hadInvalidHas = true\n    }\n    if (checkInvalidHasMissing(route.missing, 'missing')) {\n      hadInvalidMissing = true\n    }\n\n    if (!route.source) {\n      invalidParts.push('`source` is missing')\n    } else if (typeof route.source !== 'string') {\n      invalidParts.push('`source` is not a string')\n    } else if (!route.source.startsWith('/')) {\n      invalidParts.push('`source` does not start with /')\n    }\n\n    if (type === 'header') {\n      invalidParts.push(...checkHeader(route as Header))\n    } else if (type !== 'middleware') {\n      let _route = route as Rewrite | Redirect\n      if (!_route.destination) {\n        invalidParts.push('`destination` is missing')\n      } else if (typeof _route.destination !== 'string') {\n        invalidParts.push('`destination` is not a string')\n      } else if (\n        type === 'rewrite' &&\n        !_route.destination.match(/^(\\/|https:\\/\\/|http:\\/\\/)/)\n      ) {\n        invalidParts.push(\n          '`destination` does not start with `/`, `http://`, or `https://`'\n        )\n      }\n    }\n\n    if (type === 'redirect') {\n      const result = checkRedirect(route as Redirect)\n      hadInvalidStatus = hadInvalidStatus || result.hadInvalidStatus\n      invalidParts.push(...result.invalidParts)\n    }\n\n    let sourceTokens: Token[] | undefined\n\n    if (typeof route.source === 'string' && route.source.startsWith('/')) {\n      // only show parse error if we didn't already show error\n      // for not being a string\n      const { tokens, error, regexStr } = tryToParsePath(route.source)\n\n      if (error) {\n        invalidParts.push('`source` parse failed')\n      }\n\n      if (regexStr && regexStr.length > 4096) {\n        invalidParts.push('`source` exceeds max built length of 4096')\n      }\n\n      sourceTokens = tokens\n    }\n    const hasSegments = new Set<string>()\n\n    if (route.has) {\n      for (const hasItem of route.has) {\n        if (!hasItem.value && hasItem.key) {\n          hasSegments.add(hasItem.key)\n        }\n\n        if (hasItem.value) {\n          for (const match of hasItem.value.matchAll(namedGroupsRegex)) {\n            if (match[1]) {\n              hasSegments.add(match[1])\n            }\n          }\n\n          if (hasItem.type === 'host') {\n            hasSegments.add('host')\n          }\n        }\n      }\n    }\n\n    // make sure no unnamed patterns are attempted to be used in the\n    // destination as this can cause confusion and is not allowed\n    if (typeof (route as Rewrite).destination === 'string') {\n      if (\n        (route as Rewrite).destination.startsWith('/') &&\n        Array.isArray(sourceTokens)\n      ) {\n        const unnamedInDest = new Set()\n\n        for (const token of sourceTokens) {\n          if (typeof token === 'object' && typeof token.name === 'number') {\n            const unnamedIndex = new RegExp(`:${token.name}(?!\\\\d)`)\n            if ((route as Rewrite).destination.match(unnamedIndex)) {\n              unnamedInDest.add(`:${token.name}`)\n            }\n          }\n        }\n\n        if (unnamedInDest.size > 0) {\n          invalidParts.push(\n            `\\`destination\\` has unnamed params ${[...unnamedInDest].join(\n              ', '\n            )}`\n          )\n        } else {\n          const {\n            tokens: destTokens,\n            regexStr: destRegexStr,\n            error: destinationParseFailed,\n          } = tryToParsePath((route as Rewrite).destination, {\n            handleUrl: true,\n          })\n\n          if (destRegexStr && destRegexStr.length > 4096) {\n            invalidParts.push('`destination` exceeds max built length of 4096')\n          }\n\n          if (destinationParseFailed) {\n            invalidParts.push('`destination` parse failed')\n          } else {\n            const sourceSegments = new Set(\n              sourceTokens\n                .map((item) => typeof item === 'object' && item.name)\n                .filter(Boolean)\n            )\n            const invalidDestSegments = new Set()\n\n            for (const token of destTokens!) {\n              if (\n                typeof token === 'object' &&\n                !sourceSegments.has(token.name) &&\n                !hasSegments.has(token.name as string)\n              ) {\n                invalidDestSegments.add(token.name)\n              }\n            }\n\n            if (invalidDestSegments.size) {\n              invalidParts.push(\n                `\\`destination\\` has segments not in \\`source\\` or \\`has\\` (${[\n                  ...invalidDestSegments,\n                ].join(', ')})`\n              )\n            }\n          }\n        }\n      }\n    }\n\n    const hasInvalidKeys = invalidKeys.length > 0\n    const hasInvalidParts = invalidParts.length > 0\n\n    if (hasInvalidKeys || hasInvalidParts) {\n      console.error(\n        `${invalidParts.join(', ')}${\n          invalidKeys.length\n            ? (hasInvalidParts ? ',' : '') +\n              ` invalid field${invalidKeys.length === 1 ? '' : 's'}: ` +\n              invalidKeys.join(',')\n            : ''\n        } for route ${JSON.stringify(route)}`\n      )\n      console.error()\n      numInvalidRoutes++\n    }\n  }\n\n  if (numInvalidRoutes > 0) {\n    if (hadInvalidStatus) {\n      console.error(\n        `\\nValid redirect statusCode values are ${[...allowedStatusCodes].join(\n          ', '\n        )}`\n      )\n    }\n    if (hadInvalidHas) {\n      console.error(\n        `\\nValid \\`has\\` object shape is ${JSON.stringify(\n          {\n            type: [...allowedHasTypes].join(', '),\n            key: 'the key to check for',\n            value: 'undefined or a value string to match against',\n          },\n          null,\n          2\n        )}`\n      )\n    }\n    if (hadInvalidMissing) {\n      console.error(\n        `\\nValid \\`missing\\` object shape is ${JSON.stringify(\n          {\n            type: [...allowedHasTypes].join(', '),\n            key: 'the key to check for',\n            value: 'undefined or a value string to match against',\n          },\n          null,\n          2\n        )}`\n      )\n    }\n    console.error()\n    console.error(\n      `Error: Invalid ${type}${numInvalidRoutes === 1 ? '' : 's'} found`\n    )\n    process.exit(1)\n  }\n}\n\nexport interface CustomRoutes {\n  headers: Header[]\n  rewrites: {\n    fallback: Rewrite[]\n    afterFiles: Rewrite[]\n    beforeFiles: Rewrite[]\n  }\n  redirects: Redirect[]\n}\n\nfunction processRoutes<T>(\n  routes: T,\n  config: NextConfig,\n  type: 'redirect' | 'rewrite' | 'header'\n): T {\n  const _routes = routes as any as Array<{\n    source: string\n    locale?: false\n    basePath?: false\n    destination?: string\n  }>\n  const newRoutes: typeof _routes = []\n  const defaultLocales: Array<{\n    locale: string\n    base: string\n  }> = []\n\n  if (config.i18n && type === 'redirect') {\n    for (const item of config.i18n?.domains || []) {\n      defaultLocales.push({\n        locale: item.defaultLocale,\n        base: `http${item.http ? '' : 's'}://${item.domain}`,\n      })\n    }\n\n    defaultLocales.push({\n      locale: config.i18n.defaultLocale,\n      base: '',\n    })\n  }\n\n  for (const r of _routes) {\n    const srcBasePath =\n      config.basePath && r.basePath !== false ? config.basePath : ''\n    const isExternal = !r.destination?.startsWith('/')\n    const destBasePath = srcBasePath && !isExternal ? srcBasePath : ''\n\n    if (config.i18n && r.locale !== false) {\n      if (!isExternal) {\n        defaultLocales.forEach((item) => {\n          let destination\n\n          if (r.destination) {\n            destination = item.base\n              ? `${item.base}${destBasePath}${r.destination}`\n              : `${destBasePath}${r.destination}`\n          }\n\n          newRoutes.push({\n            ...r,\n            destination,\n            source: `${srcBasePath}/${item.locale}${\n              r.source === '/' && !config.trailingSlash ? '' : r.source\n            }`,\n          })\n        })\n      }\n\n      r.source = `/:nextInternalLocale(${config.i18n.locales\n        .map((locale: string) => escapeStringRegexp(locale))\n        .join('|')})${\n        r.source === '/' && !config.trailingSlash ? '' : r.source\n      }`\n\n      if (r.destination && r.destination?.startsWith('/')) {\n        r.destination = `/:nextInternalLocale${\n          r.destination === '/' && !config.trailingSlash ? '' : r.destination\n        }`\n      }\n    }\n    r.source = `${srcBasePath}${\n      r.source === '/' && srcBasePath ? '' : r.source\n    }`\n\n    if (r.destination) {\n      r.destination = `${destBasePath}${\n        r.destination === '/' && destBasePath ? '' : r.destination\n      }`\n    }\n    newRoutes.push(r)\n  }\n  return newRoutes as any as T\n}\n\nasync function loadRedirects(config: NextConfig) {\n  if (typeof config.redirects !== 'function') {\n    return []\n  }\n  let redirects = await config.redirects()\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(redirects, 'redirect')\n\n  // save original redirects before transforms\n  if (Array.isArray(redirects)) {\n    config._originalRedirects = redirects.map((r) => ({ ...r }))\n  }\n  redirects = processRoutes(redirects, config, 'redirect')\n  checkCustomRoutes(redirects, 'redirect')\n  return redirects\n}\n\nasync function loadRewrites(config: NextConfig) {\n  // If assetPrefix is set, add a rewrite for `/${assetPrefix}/_next/*`\n  // requests so that they are handled in any of dev, start, or deploy\n  // automatically without the user having to configure this.\n  // If the assetPrefix is an absolute URL, we still consider the path for automatic rewrite.\n  // but hostname routing must be handled by the user\n  let maybeAssetPrefixRewrite: Rewrite[] = []\n  if (config.assetPrefix) {\n    let prefix = config.assetPrefix\n    if (\n      isFullStringUrl(config.assetPrefix) &&\n      URL.canParse(config.assetPrefix)\n    ) {\n      prefix = new URL(config.assetPrefix).pathname\n    }\n\n    if (prefix && prefix !== '/') {\n      const assetPrefix = prefix.startsWith('/') ? prefix : `/${prefix}`\n      const basePath = config.basePath || ''\n      // If these are the same, then this would result in an infinite rewrite.\n      if (assetPrefix !== basePath) {\n        maybeAssetPrefixRewrite.push({\n          source: `${assetPrefix}/_next/:path+`,\n          destination: `${basePath}/_next/:path+`,\n        })\n      }\n    }\n  }\n\n  if (typeof config.rewrites !== 'function') {\n    return {\n      beforeFiles: [...maybeAssetPrefixRewrite],\n      afterFiles: [],\n      fallback: [],\n    }\n  }\n  const _rewrites = await config.rewrites()\n  let beforeFiles: Rewrite[] = []\n  let afterFiles: Rewrite[] = []\n  let fallback: Rewrite[] = []\n\n  if (\n    !Array.isArray(_rewrites) &&\n    typeof _rewrites === 'object' &&\n    Object.keys(_rewrites).every(\n      (key) =>\n        key === 'beforeFiles' || key === 'afterFiles' || key === 'fallback'\n    )\n  ) {\n    beforeFiles = _rewrites.beforeFiles || []\n    afterFiles = _rewrites.afterFiles || []\n    fallback = _rewrites.fallback || []\n  } else {\n    afterFiles = _rewrites as any\n  }\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(beforeFiles, 'rewrite')\n  checkCustomRoutes(afterFiles, 'rewrite')\n  checkCustomRoutes(fallback, 'rewrite')\n\n  // save original rewrites before transforms\n  config._originalRewrites = {\n    beforeFiles: beforeFiles.map((r) => ({ ...r })),\n    afterFiles: afterFiles.map((r) => ({ ...r })),\n    fallback: fallback.map((r) => ({ ...r })),\n  }\n\n  beforeFiles = [\n    ...maybeAssetPrefixRewrite,\n    ...processRoutes(beforeFiles, config, 'rewrite'),\n  ]\n  afterFiles = processRoutes(afterFiles, config, 'rewrite')\n  fallback = processRoutes(fallback, config, 'rewrite')\n\n  checkCustomRoutes(beforeFiles, 'rewrite')\n  checkCustomRoutes(afterFiles, 'rewrite')\n  checkCustomRoutes(fallback, 'rewrite')\n\n  return {\n    beforeFiles,\n    afterFiles,\n    fallback,\n  }\n}\n\nasync function loadHeaders(config: NextConfig) {\n  if (typeof config.headers !== 'function') {\n    return []\n  }\n  let headers = await config.headers()\n  // check before we process the routes and after to ensure\n  // they are still valid\n  checkCustomRoutes(headers, 'header')\n\n  headers = processRoutes(headers, config, 'header')\n  checkCustomRoutes(headers, 'header')\n  return headers\n}\n\nexport default async function loadCustomRoutes(\n  config: NextConfig\n): Promise<CustomRoutes> {\n  const [headers, rewrites, redirects] = await Promise.all([\n    loadHeaders(config),\n    loadRewrites(config),\n    loadRedirects(config),\n  ])\n\n  const totalRewrites =\n    rewrites.beforeFiles.length +\n    rewrites.afterFiles.length +\n    rewrites.fallback.length\n\n  const totalRoutes = headers.length + redirects.length + totalRewrites\n\n  if (totalRoutes > 1000) {\n    console.warn(\n      bold(yellow(`Warning: `)) +\n        `total number of custom routes exceeds 1000, this can reduce performance. Route counts:\\n` +\n        `headers: ${headers.length}\\n` +\n        `rewrites: ${totalRewrites}\\n` +\n        `redirects: ${redirects.length}\\n` +\n        `See more info: https://nextjs.org/docs/messages/max-custom-routes-reached`\n    )\n  }\n\n  if (!config.skipTrailingSlashRedirect) {\n    if (config.trailingSlash) {\n      redirects.unshift(\n        {\n          source: '/:file((?!\\\\.well-known(?:/.*)?)(?:[^/]+/)*[^/]+\\\\.\\\\w+)/',\n          destination: '/:file',\n          permanent: true,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n          // don't run this redirect for _next/data requests\n          missing: [\n            {\n              type: 'header',\n              key: 'x-nextjs-data',\n            },\n          ],\n        },\n        {\n          source: '/:notfile((?!\\\\.well-known(?:/.*)?)(?:[^/]+/)*[^/\\\\.]+)',\n          destination: '/:notfile/',\n          permanent: true,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n        }\n      )\n      if (config.basePath) {\n        redirects.unshift({\n          source: config.basePath,\n          destination: config.basePath + '/',\n          permanent: true,\n          basePath: false,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n        })\n      }\n    } else {\n      redirects.unshift({\n        source: '/:path+/',\n        destination: '/:path+',\n        permanent: true,\n        locale: config.i18n ? false : undefined,\n        internal: true,\n      })\n      if (config.basePath) {\n        redirects.unshift({\n          source: config.basePath + '/',\n          destination: config.basePath,\n          permanent: true,\n          basePath: false,\n          locale: config.i18n ? false : undefined,\n          internal: true,\n        })\n      }\n    }\n  }\n\n  return {\n    headers,\n    rewrites,\n    redirects,\n  }\n}\n"], "names": ["checkCustomRoutes", "loadCustomRoutes", "normalizeRouteRegex", "allowedHasTypes", "Set", "namedGroupsRegex", "regex", "replace", "checkRedirect", "route", "invalidParts", "hadInvalidStatus", "statusCode", "allowedStatusCodes", "has", "push", "permanent", "checkHeader", "Array", "isArray", "headers", "length", "header", "key", "value", "routes", "type", "console", "error", "process", "exit", "numInvalidRoutes", "hadInvalidHas", "hadInvalidMissing", "<PERSON><PERSON><PERSON><PERSON>", "add", "JSON", "stringify", "basePath", "destination", "startsWith", "source", "keys", "Object", "<PERSON><PERSON><PERSON><PERSON>", "filter", "locale", "checkInvalidHasMissing", "items", "fieldName", "hadInvalidItem", "invalidHasItems", "hasItem", "invalidHasParts", "join", "itemStr", "missing", "_route", "match", "result", "sourceTokens", "tokens", "regexStr", "tryToParsePath", "hasSegments", "matchAll", "unnamedInDest", "token", "name", "unnamedIndex", "RegExp", "size", "destTokens", "destRegexStr", "destinationParseFailed", "handleUrl", "sourceSegments", "map", "item", "Boolean", "invalidDestSegments", "hasInvalidKeys", "hasInvalidParts", "processRoutes", "config", "_routes", "newRoutes", "defaultLocales", "i18n", "domains", "defaultLocale", "base", "http", "domain", "r", "srcBasePath", "isExternal", "destBasePath", "for<PERSON>ach", "trailingSlash", "locales", "escapeStringRegexp", "loadRedirects", "redirects", "_originalRedirects", "loadRewrites", "maybeAssetPrefixRewrite", "assetPrefix", "prefix", "isFullStringUrl", "URL", "canParse", "pathname", "rewrites", "beforeFiles", "afterFiles", "fallback", "_rewrites", "every", "_originalRewrites", "loadHeaders", "Promise", "all", "totalRewrites", "totalRoutes", "warn", "bold", "yellow", "skipTrailingSlashRedirect", "unshift", "undefined", "internal"], "mappings": ";;;;;;;;;;;;;;;;IA2IgBA,iBAAiB;eAAjBA;;IA2iBhB,OAwFC;eAxF6BC;;IAnmBdC,mBAAmB;eAAnBA;;;4BAhFa;8BACM;gCACJ;gCACI;qBACH;AAyEhC,MAAMC,kBAAkB,IAAIC,IAAI;IAAC;IAAU;IAAU;IAAS;CAAO;AACrE,MAAMC,mBAAmB;AAElB,SAASH,oBAAoBI,KAAa;IAC/C,0EAA0E;IAC1E,OAAOA,MAAMC,OAAO,CAAC,SAAS;AAChC;AAEA,SAASC,cAAcC,KAAe;IAIpC,MAAMC,eAAyB,EAAE;IACjC,IAAIC,mBAA4B;IAEhC,IAAIF,MAAMG,UAAU,IAAI,CAACC,kCAAkB,CAACC,GAAG,CAACL,KAAK,CAAC,aAAa,GAAG;QACpEE,mBAAmB;QACnBD,aAAaK,IAAI,CAAC,CAAC,mDAAmD,CAAC;IACzE;IACA,IAAI,OAAON,MAAMO,SAAS,KAAK,aAAa,CAACP,KAAK,CAAC,aAAa,EAAE;QAChEC,aAAaK,IAAI,CAAC,CAAC,iDAAiD,CAAC;IACvE;IAEA,OAAO;QACLL;QACAC;IACF;AACF;AAEA,SAASM,YAAYR,KAAa;IAChC,MAAMC,eAAyB,EAAE;IAEjC,IAAI,CAACQ,MAAMC,OAAO,CAACV,MAAMW,OAAO,GAAG;QACjCV,aAAaK,IAAI,CAAC;IACpB,OAAO,IAAIN,MAAMW,OAAO,CAACC,MAAM,KAAK,GAAG;QACrCX,aAAaK,IAAI,CAAC;IACpB,OAAO;QACL,KAAK,MAAMO,UAAUb,MAAMW,OAAO,CAAE;YAClC,IAAI,CAACE,UAAU,OAAOA,WAAW,UAAU;gBACzCZ,aAAaK,IAAI,CACf;gBAEF;YACF;YACA,IAAI,OAAOO,OAAOC,GAAG,KAAK,UAAU;gBAClCb,aAAaK,IAAI,CAAC;gBAClB;YACF;YACA,IAAI,OAAOO,OAAOE,KAAK,KAAK,UAAU;gBACpCd,aAAaK,IAAI,CAAC;gBAClB;YACF;QACF;IACF;IACA,OAAOL;AACT;AAIO,SAASV,kBACdyB,MAAwD,EACxDC,IAA8B;IAE9B,IAAI,CAACR,MAAMC,OAAO,CAACM,SAAS;QAC1BE,QAAQC,KAAK,CACX,CAAC,OAAO,EAAEF,KAAK,iCAAiC,EAAE,OAAOD,OAAO,GAAG,CAAC,GAClE,CAAC,6EAA6E,CAAC;QAEnFI,QAAQC,IAAI,CAAC;IACf;IAEA,IAAIC,mBAAmB;IACvB,IAAIpB,mBAAmB;IACvB,IAAIqB,gBAAgB;IACpB,IAAIC,oBAAoB;IAExB,MAAMC,cAAc,IAAI9B,IAAY;QAAC;QAAU;QAAU;QAAO;KAAU;IAE1E,IAAIsB,SAAS,WAAW;QACtBQ,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;IAClB;IACA,IAAIT,SAAS,YAAY;QACvBQ,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;IAClB;IACA,IAAIT,SAAS,UAAU;QACrBQ,YAAYC,GAAG,CAAC;QAChBD,YAAYC,GAAG,CAAC;IAClB;IAEA,KAAK,MAAM1B,SAASgB,OAAQ;QAC1B,IAAI,CAAChB,SAAS,OAAOA,UAAU,UAAU;YACvCkB,QAAQC,KAAK,CACX,CAAC,UAAU,EAAEQ,KAAKC,SAAS,CACzB5B,OACA,sCAAsC,EACtCiB,SAAS,eACL,CAAC,OAAO,EAAEA,SAAS,WAAW,YAAY,cAAc,EAAE,CAAC,GAC3D,IACJ;YAEJK;YACA;QACF;QAEA,IACEL,SAAS,aACT,AAACjB,MAAkB6B,QAAQ,KAAK,SAChC,CACE,CAAA,AAAC7B,MAAkB8B,WAAW,CAACC,UAAU,CAAC,cAC1C,AAAC/B,MAAkB8B,WAAW,CAACC,UAAU,CAAC,WAAU,GAEtD;YACAb,QAAQC,KAAK,CACX,CAAC,UAAU,EACT,AAACnB,MAAkBgC,MAAM,CAC1B,uKAAuK,CAAC;YAE3KV;YACA;QACF;QAEA,MAAMW,OAAOC,OAAOD,IAAI,CAACjC;QACzB,MAAMmC,cAAcF,KAAKG,MAAM,CAAC,CAACtB,MAAQ,CAACW,YAAYpB,GAAG,CAACS;QAC1D,MAAMb,eAAyB,EAAE;QAEjC,IACE,cAAcD,SACd,OAAOA,MAAM6B,QAAQ,KAAK,eAC1B7B,MAAM6B,QAAQ,KAAK,OACnB;YACA5B,aAAaK,IAAI,CAAC;QACpB;QAEA,IAAI,OAAON,MAAMqC,MAAM,KAAK,eAAerC,MAAMqC,MAAM,KAAK,OAAO;YACjEpC,aAAaK,IAAI,CAAC;QACpB;QAEA,MAAMgC,yBAAyB,CAC7BC,OACAC;YAEA,IAAIC,iBAAiB;YAErB,IAAI,OAAOF,UAAU,eAAe,CAAC9B,MAAMC,OAAO,CAAC6B,QAAQ;gBACzDtC,aAAaK,IAAI,CACf,CAAC,EAAE,EAAEkC,UAAU,wCAAwC,CAAC;gBAE1DC,iBAAiB;YACnB,OAAO,IAAIF,OAAO;gBAChB,MAAMG,kBAAkB,EAAE;gBAE1B,KAAK,MAAMC,WAAWJ,MAAO;oBAC3B,IAAIK,kBAAkB,EAAE;oBAExB,IAAI,CAAClD,gBAAgBW,GAAG,CAACsC,QAAQ1B,IAAI,GAAG;wBACtC2B,gBAAgBtC,IAAI,CAAC,CAAC,cAAc,EAAEqC,QAAQ1B,IAAI,CAAC,CAAC,CAAC;oBACvD;oBACA,IAAI,OAAO0B,QAAQ7B,GAAG,KAAK,YAAY6B,QAAQ1B,IAAI,KAAK,QAAQ;wBAC9D2B,gBAAgBtC,IAAI,CAAC,CAAC,aAAa,EAAEqC,QAAQ7B,GAAG,CAAC,CAAC,CAAC;oBACrD;oBACA,IACE,OAAO6B,QAAQ5B,KAAK,KAAK,eACzB,OAAO4B,QAAQ5B,KAAK,KAAK,UACzB;wBACA6B,gBAAgBtC,IAAI,CAAC,CAAC,eAAe,EAAEqC,QAAQ5B,KAAK,CAAC,CAAC,CAAC;oBACzD;oBACA,IAAI,OAAO4B,QAAQ5B,KAAK,KAAK,eAAe4B,QAAQ1B,IAAI,KAAK,QAAQ;wBACnE2B,gBAAgBtC,IAAI,CAAC,CAAC,iCAAiC,CAAC;oBAC1D;oBAEA,IAAIsC,gBAAgBhC,MAAM,GAAG,GAAG;wBAC9B8B,gBAAgBpC,IAAI,CAClB,GAAGsC,gBAAgBC,IAAI,CAAC,MAAM,KAAK,EAAElB,KAAKC,SAAS,CAACe,UAAU;oBAElE;gBACF;gBAEA,IAAID,gBAAgB9B,MAAM,GAAG,GAAG;oBAC9B6B,iBAAiB;oBACjB,MAAMK,UAAU,CAAC,IAAI,EAAEJ,gBAAgB9B,MAAM,KAAK,IAAI,KAAK,KAAK;oBAEhEM,QAAQC,KAAK,CACX,CAAC,UAAU,EAAEqB,UAAU,GAAG,EAAEM,QAAQ,GAAG,CAAC,GACtCJ,gBAAgBG,IAAI,CAAC;oBAEzB3B,QAAQC,KAAK;oBACblB,aAAaK,IAAI,CAAC,CAAC,UAAU,EAAEkC,UAAU,GAAG,EAAEM,QAAQ,MAAM,CAAC;gBAC/D;YACF;YACA,OAAOL;QACT;QACA,IAAIH,uBAAuBtC,MAAMK,GAAG,EAAE,QAAQ;YAC5CkB,gBAAgB;QAClB;QACA,IAAIe,uBAAuBtC,MAAM+C,OAAO,EAAE,YAAY;YACpDvB,oBAAoB;QACtB;QAEA,IAAI,CAACxB,MAAMgC,MAAM,EAAE;YACjB/B,aAAaK,IAAI,CAAC;QACpB,OAAO,IAAI,OAAON,MAAMgC,MAAM,KAAK,UAAU;YAC3C/B,aAAaK,IAAI,CAAC;QACpB,OAAO,IAAI,CAACN,MAAMgC,MAAM,CAACD,UAAU,CAAC,MAAM;YACxC9B,aAAaK,IAAI,CAAC;QACpB;QAEA,IAAIW,SAAS,UAAU;YACrBhB,aAAaK,IAAI,IAAIE,YAAYR;QACnC,OAAO,IAAIiB,SAAS,cAAc;YAChC,IAAI+B,SAAShD;YACb,IAAI,CAACgD,OAAOlB,WAAW,EAAE;gBACvB7B,aAAaK,IAAI,CAAC;YACpB,OAAO,IAAI,OAAO0C,OAAOlB,WAAW,KAAK,UAAU;gBACjD7B,aAAaK,IAAI,CAAC;YACpB,OAAO,IACLW,SAAS,aACT,CAAC+B,OAAOlB,WAAW,CAACmB,KAAK,CAAC,+BAC1B;gBACAhD,aAAaK,IAAI,CACf;YAEJ;QACF;QAEA,IAAIW,SAAS,YAAY;YACvB,MAAMiC,SAASnD,cAAcC;YAC7BE,mBAAmBA,oBAAoBgD,OAAOhD,gBAAgB;YAC9DD,aAAaK,IAAI,IAAI4C,OAAOjD,YAAY;QAC1C;QAEA,IAAIkD;QAEJ,IAAI,OAAOnD,MAAMgC,MAAM,KAAK,YAAYhC,MAAMgC,MAAM,CAACD,UAAU,CAAC,MAAM;YACpE,wDAAwD;YACxD,yBAAyB;YACzB,MAAM,EAAEqB,MAAM,EAAEjC,KAAK,EAAEkC,QAAQ,EAAE,GAAGC,IAAAA,8BAAc,EAACtD,MAAMgC,MAAM;YAE/D,IAAIb,OAAO;gBACTlB,aAAaK,IAAI,CAAC;YACpB;YAEA,IAAI+C,YAAYA,SAASzC,MAAM,GAAG,MAAM;gBACtCX,aAAaK,IAAI,CAAC;YACpB;YAEA6C,eAAeC;QACjB;QACA,MAAMG,cAAc,IAAI5D;QAExB,IAAIK,MAAMK,GAAG,EAAE;YACb,KAAK,MAAMsC,WAAW3C,MAAMK,GAAG,CAAE;gBAC/B,IAAI,CAACsC,QAAQ5B,KAAK,IAAI4B,QAAQ7B,GAAG,EAAE;oBACjCyC,YAAY7B,GAAG,CAACiB,QAAQ7B,GAAG;gBAC7B;gBAEA,IAAI6B,QAAQ5B,KAAK,EAAE;oBACjB,KAAK,MAAMkC,SAASN,QAAQ5B,KAAK,CAACyC,QAAQ,CAAC5D,kBAAmB;wBAC5D,IAAIqD,KAAK,CAAC,EAAE,EAAE;4BACZM,YAAY7B,GAAG,CAACuB,KAAK,CAAC,EAAE;wBAC1B;oBACF;oBAEA,IAAIN,QAAQ1B,IAAI,KAAK,QAAQ;wBAC3BsC,YAAY7B,GAAG,CAAC;oBAClB;gBACF;YACF;QACF;QAEA,gEAAgE;QAChE,6DAA6D;QAC7D,IAAI,OAAO,AAAC1B,MAAkB8B,WAAW,KAAK,UAAU;YACtD,IACE,AAAC9B,MAAkB8B,WAAW,CAACC,UAAU,CAAC,QAC1CtB,MAAMC,OAAO,CAACyC,eACd;gBACA,MAAMM,gBAAgB,IAAI9D;gBAE1B,KAAK,MAAM+D,SAASP,aAAc;oBAChC,IAAI,OAAOO,UAAU,YAAY,OAAOA,MAAMC,IAAI,KAAK,UAAU;wBAC/D,MAAMC,eAAe,IAAIC,OAAO,CAAC,CAAC,EAAEH,MAAMC,IAAI,CAAC,OAAO,CAAC;wBACvD,IAAI,AAAC3D,MAAkB8B,WAAW,CAACmB,KAAK,CAACW,eAAe;4BACtDH,cAAc/B,GAAG,CAAC,CAAC,CAAC,EAAEgC,MAAMC,IAAI,EAAE;wBACpC;oBACF;gBACF;gBAEA,IAAIF,cAAcK,IAAI,GAAG,GAAG;oBAC1B7D,aAAaK,IAAI,CACf,CAAC,mCAAmC,EAAE;2BAAImD;qBAAc,CAACZ,IAAI,CAC3D,OACC;gBAEP,OAAO;oBACL,MAAM,EACJO,QAAQW,UAAU,EAClBV,UAAUW,YAAY,EACtB7C,OAAO8C,sBAAsB,EAC9B,GAAGX,IAAAA,8BAAc,EAAC,AAACtD,MAAkB8B,WAAW,EAAE;wBACjDoC,WAAW;oBACb;oBAEA,IAAIF,gBAAgBA,aAAapD,MAAM,GAAG,MAAM;wBAC9CX,aAAaK,IAAI,CAAC;oBACpB;oBAEA,IAAI2D,wBAAwB;wBAC1BhE,aAAaK,IAAI,CAAC;oBACpB,OAAO;wBACL,MAAM6D,iBAAiB,IAAIxE,IACzBwD,aACGiB,GAAG,CAAC,CAACC,OAAS,OAAOA,SAAS,YAAYA,KAAKV,IAAI,EACnDvB,MAAM,CAACkC;wBAEZ,MAAMC,sBAAsB,IAAI5E;wBAEhC,KAAK,MAAM+D,SAASK,WAAa;4BAC/B,IACE,OAAOL,UAAU,YACjB,CAACS,eAAe9D,GAAG,CAACqD,MAAMC,IAAI,KAC9B,CAACJ,YAAYlD,GAAG,CAACqD,MAAMC,IAAI,GAC3B;gCACAY,oBAAoB7C,GAAG,CAACgC,MAAMC,IAAI;4BACpC;wBACF;wBAEA,IAAIY,oBAAoBT,IAAI,EAAE;4BAC5B7D,aAAaK,IAAI,CACf,CAAC,2DAA2D,EAAE;mCACzDiE;6BACJ,CAAC1B,IAAI,CAAC,MAAM,CAAC,CAAC;wBAEnB;oBACF;gBACF;YACF;QACF;QAEA,MAAM2B,iBAAiBrC,YAAYvB,MAAM,GAAG;QAC5C,MAAM6D,kBAAkBxE,aAAaW,MAAM,GAAG;QAE9C,IAAI4D,kBAAkBC,iBAAiB;YACrCvD,QAAQC,KAAK,CACX,GAAGlB,aAAa4C,IAAI,CAAC,QACnBV,YAAYvB,MAAM,GACd,AAAC6D,CAAAA,kBAAkB,MAAM,EAAC,IAC1B,CAAC,cAAc,EAAEtC,YAAYvB,MAAM,KAAK,IAAI,KAAK,IAAI,EAAE,CAAC,GACxDuB,YAAYU,IAAI,CAAC,OACjB,GACL,WAAW,EAAElB,KAAKC,SAAS,CAAC5B,QAAQ;YAEvCkB,QAAQC,KAAK;YACbG;QACF;IACF;IAEA,IAAIA,mBAAmB,GAAG;QACxB,IAAIpB,kBAAkB;YACpBgB,QAAQC,KAAK,CACX,CAAC,uCAAuC,EAAE;mBAAIf,kCAAkB;aAAC,CAACyC,IAAI,CACpE,OACC;QAEP;QACA,IAAItB,eAAe;YACjBL,QAAQC,KAAK,CACX,CAAC,gCAAgC,EAAEQ,KAAKC,SAAS,CAC/C;gBACEX,MAAM;uBAAIvB;iBAAgB,CAACmD,IAAI,CAAC;gBAChC/B,KAAK;gBACLC,OAAO;YACT,GACA,MACA,IACC;QAEP;QACA,IAAIS,mBAAmB;YACrBN,QAAQC,KAAK,CACX,CAAC,oCAAoC,EAAEQ,KAAKC,SAAS,CACnD;gBACEX,MAAM;uBAAIvB;iBAAgB,CAACmD,IAAI,CAAC;gBAChC/B,KAAK;gBACLC,OAAO;YACT,GACA,MACA,IACC;QAEP;QACAG,QAAQC,KAAK;QACbD,QAAQC,KAAK,CACX,CAAC,eAAe,EAAEF,OAAOK,qBAAqB,IAAI,KAAK,IAAI,MAAM,CAAC;QAEpEF,QAAQC,IAAI,CAAC;IACf;AACF;AAYA,SAASqD,cACP1D,MAAS,EACT2D,MAAkB,EAClB1D,IAAuC;IAEvC,MAAM2D,UAAU5D;IAMhB,MAAM6D,YAA4B,EAAE;IACpC,MAAMC,iBAGD,EAAE;IAEP,IAAIH,OAAOI,IAAI,IAAI9D,SAAS,YAAY;YACnB0D;QAAnB,KAAK,MAAMN,QAAQM,EAAAA,eAAAA,OAAOI,IAAI,qBAAXJ,aAAaK,OAAO,KAAI,EAAE,CAAE;YAC7CF,eAAexE,IAAI,CAAC;gBAClB+B,QAAQgC,KAAKY,aAAa;gBAC1BC,MAAM,CAAC,IAAI,EAAEb,KAAKc,IAAI,GAAG,KAAK,IAAI,GAAG,EAAEd,KAAKe,MAAM,EAAE;YACtD;QACF;QAEAN,eAAexE,IAAI,CAAC;YAClB+B,QAAQsC,OAAOI,IAAI,CAACE,aAAa;YACjCC,MAAM;QACR;IACF;IAEA,KAAK,MAAMG,KAAKT,QAAS;YAGHS;QAFpB,MAAMC,cACJX,OAAO9C,QAAQ,IAAIwD,EAAExD,QAAQ,KAAK,QAAQ8C,OAAO9C,QAAQ,GAAG;QAC9D,MAAM0D,aAAa,GAACF,iBAAAA,EAAEvD,WAAW,qBAAbuD,eAAetD,UAAU,CAAC;QAC9C,MAAMyD,eAAeF,eAAe,CAACC,aAAaD,cAAc;QAEhE,IAAIX,OAAOI,IAAI,IAAIM,EAAEhD,MAAM,KAAK,OAAO;gBA2BhBgD;YA1BrB,IAAI,CAACE,YAAY;gBACfT,eAAeW,OAAO,CAAC,CAACpB;oBACtB,IAAIvC;oBAEJ,IAAIuD,EAAEvD,WAAW,EAAE;wBACjBA,cAAcuC,KAAKa,IAAI,GACnB,GAAGb,KAAKa,IAAI,GAAGM,eAAeH,EAAEvD,WAAW,EAAE,GAC7C,GAAG0D,eAAeH,EAAEvD,WAAW,EAAE;oBACvC;oBAEA+C,UAAUvE,IAAI,CAAC;wBACb,GAAG+E,CAAC;wBACJvD;wBACAE,QAAQ,GAAGsD,YAAY,CAAC,EAAEjB,KAAKhC,MAAM,GACnCgD,EAAErD,MAAM,KAAK,OAAO,CAAC2C,OAAOe,aAAa,GAAG,KAAKL,EAAErD,MAAM,EACzD;oBACJ;gBACF;YACF;YAEAqD,EAAErD,MAAM,GAAG,CAAC,qBAAqB,EAAE2C,OAAOI,IAAI,CAACY,OAAO,CACnDvB,GAAG,CAAC,CAAC/B,SAAmBuD,IAAAA,gCAAkB,EAACvD,SAC3CQ,IAAI,CAAC,KAAK,CAAC,EACZwC,EAAErD,MAAM,KAAK,OAAO,CAAC2C,OAAOe,aAAa,GAAG,KAAKL,EAAErD,MAAM,EACzD;YAEF,IAAIqD,EAAEvD,WAAW,MAAIuD,kBAAAA,EAAEvD,WAAW,qBAAbuD,gBAAetD,UAAU,CAAC,OAAM;gBACnDsD,EAAEvD,WAAW,GAAG,CAAC,oBAAoB,EACnCuD,EAAEvD,WAAW,KAAK,OAAO,CAAC6C,OAAOe,aAAa,GAAG,KAAKL,EAAEvD,WAAW,EACnE;YACJ;QACF;QACAuD,EAAErD,MAAM,GAAG,GAAGsD,cACZD,EAAErD,MAAM,KAAK,OAAOsD,cAAc,KAAKD,EAAErD,MAAM,EAC/C;QAEF,IAAIqD,EAAEvD,WAAW,EAAE;YACjBuD,EAAEvD,WAAW,GAAG,GAAG0D,eACjBH,EAAEvD,WAAW,KAAK,OAAO0D,eAAe,KAAKH,EAAEvD,WAAW,EAC1D;QACJ;QACA+C,UAAUvE,IAAI,CAAC+E;IACjB;IACA,OAAOR;AACT;AAEA,eAAegB,cAAclB,MAAkB;IAC7C,IAAI,OAAOA,OAAOmB,SAAS,KAAK,YAAY;QAC1C,OAAO,EAAE;IACX;IACA,IAAIA,YAAY,MAAMnB,OAAOmB,SAAS;IACtC,yDAAyD;IACzD,uBAAuB;IACvBvG,kBAAkBuG,WAAW;IAE7B,4CAA4C;IAC5C,IAAIrF,MAAMC,OAAO,CAACoF,YAAY;QAC5BnB,OAAOoB,kBAAkB,GAAGD,UAAU1B,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;IAC3D;IACAS,YAAYpB,cAAcoB,WAAWnB,QAAQ;IAC7CpF,kBAAkBuG,WAAW;IAC7B,OAAOA;AACT;AAEA,eAAeE,aAAarB,MAAkB;IAC5C,qEAAqE;IACrE,oEAAoE;IACpE,2DAA2D;IAC3D,2FAA2F;IAC3F,mDAAmD;IACnD,IAAIsB,0BAAqC,EAAE;IAC3C,IAAItB,OAAOuB,WAAW,EAAE;QACtB,IAAIC,SAASxB,OAAOuB,WAAW;QAC/B,IACEE,IAAAA,oBAAe,EAACzB,OAAOuB,WAAW,KAClCG,IAAIC,QAAQ,CAAC3B,OAAOuB,WAAW,GAC/B;YACAC,SAAS,IAAIE,IAAI1B,OAAOuB,WAAW,EAAEK,QAAQ;QAC/C;QAEA,IAAIJ,UAAUA,WAAW,KAAK;YAC5B,MAAMD,cAAcC,OAAOpE,UAAU,CAAC,OAAOoE,SAAS,CAAC,CAAC,EAAEA,QAAQ;YAClE,MAAMtE,WAAW8C,OAAO9C,QAAQ,IAAI;YACpC,wEAAwE;YACxE,IAAIqE,gBAAgBrE,UAAU;gBAC5BoE,wBAAwB3F,IAAI,CAAC;oBAC3B0B,QAAQ,GAAGkE,YAAY,aAAa,CAAC;oBACrCpE,aAAa,GAAGD,SAAS,aAAa,CAAC;gBACzC;YACF;QACF;IACF;IAEA,IAAI,OAAO8C,OAAO6B,QAAQ,KAAK,YAAY;QACzC,OAAO;YACLC,aAAa;mBAAIR;aAAwB;YACzCS,YAAY,EAAE;YACdC,UAAU,EAAE;QACd;IACF;IACA,MAAMC,YAAY,MAAMjC,OAAO6B,QAAQ;IACvC,IAAIC,cAAyB,EAAE;IAC/B,IAAIC,aAAwB,EAAE;IAC9B,IAAIC,WAAsB,EAAE;IAE5B,IACE,CAAClG,MAAMC,OAAO,CAACkG,cACf,OAAOA,cAAc,YACrB1E,OAAOD,IAAI,CAAC2E,WAAWC,KAAK,CAC1B,CAAC/F,MACCA,QAAQ,iBAAiBA,QAAQ,gBAAgBA,QAAQ,aAE7D;QACA2F,cAAcG,UAAUH,WAAW,IAAI,EAAE;QACzCC,aAAaE,UAAUF,UAAU,IAAI,EAAE;QACvCC,WAAWC,UAAUD,QAAQ,IAAI,EAAE;IACrC,OAAO;QACLD,aAAaE;IACf;IACA,yDAAyD;IACzD,uBAAuB;IACvBrH,kBAAkBkH,aAAa;IAC/BlH,kBAAkBmH,YAAY;IAC9BnH,kBAAkBoH,UAAU;IAE5B,2CAA2C;IAC3ChC,OAAOmC,iBAAiB,GAAG;QACzBL,aAAaA,YAAYrC,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;QAC5CqB,YAAYA,WAAWtC,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;QAC1CsB,UAAUA,SAASvC,GAAG,CAAC,CAACiB,IAAO,CAAA;gBAAE,GAAGA,CAAC;YAAC,CAAA;IACxC;IAEAoB,cAAc;WACTR;WACAvB,cAAc+B,aAAa9B,QAAQ;KACvC;IACD+B,aAAahC,cAAcgC,YAAY/B,QAAQ;IAC/CgC,WAAWjC,cAAciC,UAAUhC,QAAQ;IAE3CpF,kBAAkBkH,aAAa;IAC/BlH,kBAAkBmH,YAAY;IAC9BnH,kBAAkBoH,UAAU;IAE5B,OAAO;QACLF;QACAC;QACAC;IACF;AACF;AAEA,eAAeI,YAAYpC,MAAkB;IAC3C,IAAI,OAAOA,OAAOhE,OAAO,KAAK,YAAY;QACxC,OAAO,EAAE;IACX;IACA,IAAIA,UAAU,MAAMgE,OAAOhE,OAAO;IAClC,yDAAyD;IACzD,uBAAuB;IACvBpB,kBAAkBoB,SAAS;IAE3BA,UAAU+D,cAAc/D,SAASgE,QAAQ;IACzCpF,kBAAkBoB,SAAS;IAC3B,OAAOA;AACT;AAEe,eAAenB,iBAC5BmF,MAAkB;IAElB,MAAM,CAAChE,SAAS6F,UAAUV,UAAU,GAAG,MAAMkB,QAAQC,GAAG,CAAC;QACvDF,YAAYpC;QACZqB,aAAarB;QACbkB,cAAclB;KACf;IAED,MAAMuC,gBACJV,SAASC,WAAW,CAAC7F,MAAM,GAC3B4F,SAASE,UAAU,CAAC9F,MAAM,GAC1B4F,SAASG,QAAQ,CAAC/F,MAAM;IAE1B,MAAMuG,cAAcxG,QAAQC,MAAM,GAAGkF,UAAUlF,MAAM,GAAGsG;IAExD,IAAIC,cAAc,MAAM;QACtBjG,QAAQkG,IAAI,CACVC,IAAAA,gBAAI,EAACC,IAAAA,kBAAM,EAAC,CAAC,SAAS,CAAC,KACrB,CAAC,wFAAwF,CAAC,GAC1F,CAAC,SAAS,EAAE3G,QAAQC,MAAM,CAAC,EAAE,CAAC,GAC9B,CAAC,UAAU,EAAEsG,cAAc,EAAE,CAAC,GAC9B,CAAC,WAAW,EAAEpB,UAAUlF,MAAM,CAAC,EAAE,CAAC,GAClC,CAAC,yEAAyE,CAAC;IAEjF;IAEA,IAAI,CAAC+D,OAAO4C,yBAAyB,EAAE;QACrC,IAAI5C,OAAOe,aAAa,EAAE;YACxBI,UAAU0B,OAAO,CACf;gBACExF,QAAQ;gBACRF,aAAa;gBACbvB,WAAW;gBACX8B,QAAQsC,OAAOI,IAAI,GAAG,QAAQ0C;gBAC9BC,UAAU;gBACV,kDAAkD;gBAClD3E,SAAS;oBACP;wBACE9B,MAAM;wBACNH,KAAK;oBACP;iBACD;YACH,GACA;gBACEkB,QAAQ;gBACRF,aAAa;gBACbvB,WAAW;gBACX8B,QAAQsC,OAAOI,IAAI,GAAG,QAAQ0C;gBAC9BC,UAAU;YACZ;YAEF,IAAI/C,OAAO9C,QAAQ,EAAE;gBACnBiE,UAAU0B,OAAO,CAAC;oBAChBxF,QAAQ2C,OAAO9C,QAAQ;oBACvBC,aAAa6C,OAAO9C,QAAQ,GAAG;oBAC/BtB,WAAW;oBACXsB,UAAU;oBACVQ,QAAQsC,OAAOI,IAAI,GAAG,QAAQ0C;oBAC9BC,UAAU;gBACZ;YACF;QACF,OAAO;YACL5B,UAAU0B,OAAO,CAAC;gBAChBxF,QAAQ;gBACRF,aAAa;gBACbvB,WAAW;gBACX8B,QAAQsC,OAAOI,IAAI,GAAG,QAAQ0C;gBAC9BC,UAAU;YACZ;YACA,IAAI/C,OAAO9C,QAAQ,EAAE;gBACnBiE,UAAU0B,OAAO,CAAC;oBAChBxF,QAAQ2C,OAAO9C,QAAQ,GAAG;oBAC1BC,aAAa6C,OAAO9C,QAAQ;oBAC5BtB,WAAW;oBACXsB,UAAU;oBACVQ,QAAQsC,OAAOI,IAAI,GAAG,QAAQ0C;oBAC9BC,UAAU;gBACZ;YACF;QACF;IACF;IAEA,OAAO;QACL/G;QACA6F;QACAV;IACF;AACF"}