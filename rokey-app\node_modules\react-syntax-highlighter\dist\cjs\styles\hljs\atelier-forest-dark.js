"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports["default"] = void 0;
var _default = exports["default"] = {
  "hljs-comment": {
    "color": "#9c9491"
  },
  "hljs-quote": {
    "color": "#9c9491"
  },
  "hljs-variable": {
    "color": "#f22c40"
  },
  "hljs-template-variable": {
    "color": "#f22c40"
  },
  "hljs-attribute": {
    "color": "#f22c40"
  },
  "hljs-tag": {
    "color": "#f22c40"
  },
  "hljs-name": {
    "color": "#f22c40"
  },
  "hljs-regexp": {
    "color": "#f22c40"
  },
  "hljs-link": {
    "color": "#f22c40"
  },
  "hljs-selector-id": {
    "color": "#f22c40"
  },
  "hljs-selector-class": {
    "color": "#f22c40"
  },
  "hljs-number": {
    "color": "#df5320"
  },
  "hljs-meta": {
    "color": "#df5320"
  },
  "hljs-built_in": {
    "color": "#df5320"
  },
  "hljs-builtin-name": {
    "color": "#df5320"
  },
  "hljs-literal": {
    "color": "#df5320"
  },
  "hljs-type": {
    "color": "#df5320"
  },
  "hljs-params": {
    "color": "#df5320"
  },
  "hljs-string": {
    "color": "#7b9726"
  },
  "hljs-symbol": {
    "color": "#7b9726"
  },
  "hljs-bullet": {
    "color": "#7b9726"
  },
  "hljs-title": {
    "color": "#407ee7"
  },
  "hljs-section": {
    "color": "#407ee7"
  },
  "hljs-keyword": {
    "color": "#6666ea"
  },
  "hljs-selector-tag": {
    "color": "#6666ea"
  },
  "hljs": {
    "display": "block",
    "overflowX": "auto",
    "background": "#1b1918",
    "color": "#a8a19f",
    "padding": "0.5em"
  },
  "hljs-emphasis": {
    "fontStyle": "italic"
  },
  "hljs-strong": {
    "fontWeight": "bold"
  }
};