{"version": 3, "sources": ["../../../../../../src/client/components/react-dev-overlay/ui/icons/light-icon.tsx"], "sourcesContent": ["export default function LightIcon() {\n  return (\n    <svg\n      xmlns=\"http://www.w3.org/2000/svg\"\n      width=\"20\"\n      height=\"16\"\n      viewBox=\"0 0 16 16\"\n      fill=\"none\"\n    >\n      <g clipPath=\"url(#light_icon_clip_path)\">\n        <path\n          fill=\"currentColor\"\n          fillRule=\"evenodd\"\n          d=\"M8.75.75V0h-1.5v2h1.5V.75ZM3.26 4.32l-.53-.53-.354-.353-.53-.53 1.06-***********.354.354.53.53-1.06 1.06Zm8.42-1.06.53-.53.353-.354.53-.53 1.061 1.06-.53.53-.354.354-.53.53-1.06-1.06ZM8 11.25a3.25 3.25 0 1 0 0-6.5 3.25 3.25 0 0 0 0 6.5Zm0 1.5a4.75 4.75 0 1 0 0-9.5 4.75 4.75 0 0 0 0 9.5Zm6-5.5h2v1.5h-2v-1.5Zm-13.25 0H0v1.5h2v-1.5H.75Zm1.62 5.32-.53.53 1.06 1.06.53-.53.354-.353.53-.53-1.06-1.061-.53.53-.354.354Zm10.2 ********** 1.06-1.06-.53-.53-.354-.354-.53-.53-1.06 **********.353.354ZM8.75 14v2h-1.5v-2h1.5Z\"\n          clipRule=\"evenodd\"\n        />\n      </g>\n      <defs>\n        <clipPath id=\"light_icon_clip_path\">\n          <path fill=\"currentColor\" d=\"M0 0h16v16H0z\" />\n        </clipPath>\n      </defs>\n    </svg>\n  )\n}\n"], "names": ["LightIcon", "svg", "xmlns", "width", "height", "viewBox", "fill", "g", "clipPath", "path", "fillRule", "d", "clipRule", "defs", "id"], "mappings": ";AAAA,eAAe,SAASA;IACtB,qBACE,MAACC;QACCC,OAAM;QACNC,OAAM;QACNC,QAAO;QACPC,SAAQ;QACRC,MAAK;;0BAEL,KAACC;gBAAEC,UAAS;0BACV,cAAA,KAACC;oBACCH,MAAK;oBACLI,UAAS;oBACTC,GAAE;oBACFC,UAAS;;;0BAGb,KAACC;0BACC,cAAA,KAACL;oBAASM,IAAG;8BACX,cAAA,KAACL;wBAAKH,MAAK;wBAAeK,GAAE;;;;;;AAKtC"}